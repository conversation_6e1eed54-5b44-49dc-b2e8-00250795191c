<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统出错报警</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, {{ error_color|default('#dc3545') }}, {{ error_color|default('#dc3545') }}dd);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header .subtitle {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .alert-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-left: 4px solid {{ error_color|default('#dc3545') }};
            padding: 20px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .error-details {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
            font-size: 14px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .info-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
        }
        .info-card h3 {
            margin: 0 0 10px 0;
            color: {{ error_color|default('#dc3545') }};
            font-size: 16px;
            font-weight: 600;
        }
        .info-card p {
            margin: 5px 0;
            font-size: 14px;
        }
        .trace-box {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
            font-size: 12px;
            line-height: 1.4;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px 30px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 14px;
        }
        .priority-badge {
            display: inline-block;
            background: {{ error_color|default('#dc3545') }};
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .timestamp {
            color: #6c757d;
            font-size: 14px;
        }
        .action-buttons {
            margin: 20px 0;
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            background: {{ error_color|default('#dc3545') }};
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        .btn:hover {
            background: {{ error_color|default('#dc3545') }}dd;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .stats-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .stats-row:last-child {
            border-bottom: none;
        }
        .stats-label {
            font-weight: 600;
            color: #495057;
        }
        .stats-value {
            color: #6c757d;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 邮件头部 -->
        <div class="header">
            <h1>🚨 系统出错报警</h1>
            <div class="subtitle">
                <span class="priority-badge">{{ error_level|default('ERROR') }}</span>
                <span class="timestamp">{{ error_time|default('now'|date('Y-m-d H:i:s')) }}</span>
            </div>
        </div>
        
        <!-- 邮件内容 -->
        <div class="content">
            <!-- 错误概要 -->
            <div class="alert-box">
                <h2 style="margin: 0 0 10px 0; color: {{ error_color|default('#dc3545') }};">
                    ⚠️ {{ error_title|default('系统运行异常') }}
                </h2>
                <p style="margin: 0; font-size: 16px;">
                    {{ error_message|default('未知错误') }}
                </p>
            </div>
            
            <!-- 错误详情 -->
            <div class="error-details">
                <div class="stats-row">
                    <span class="stats-label">📁 错误文件：</span>
                    <span class="stats-value">{{ error_file|default('未知文件') }}</span>
                </div>
                <div class="stats-row">
                    <span class="stats-label">📍 错误行号：</span>
                    <span class="stats-value">{{ error_line|default('未知行号') }}</span>
                </div>
                <div class="stats-row">
                    <span class="stats-label">⏰ 发生时间：</span>
                    <span class="stats-value">{{ error_time|default('now'|date('Y-m-d H:i:s')) }}</span>
                </div>
                <div class="stats-row">
                    <span class="stats-label">🏷️ 错误级别：</span>
                    <span class="stats-value">{{ error_level|default('ERROR') }}</span>
                </div>
            </div>
            
            <!-- 信息网格 -->
            <div class="info-grid">
                <!-- 服务器信息 -->
                <div class="info-card">
                    <h3>🖥️ 服务器信息</h3>
                    <div class="stats-row">
                        <span class="stats-label">服务器：</span>
                        <span class="stats-value">{{ server_info|default('pay.jiamisoft.com') }}</span>
                    </div>
                    <div class="stats-row">
                        <span class="stats-label">PHP版本：</span>
                        <span class="stats-value">{{ php_version|default('8.0+') }}</span>
                    </div>
                    <div class="stats-row">
                        <span class="stats-label">内存使用：</span>
                        <span class="stats-value">{{ memory_usage|default('N/A') }}</span>
                    </div>
                    <div class="stats-row">
                        <span class="stats-label">峰值内存：</span>
                        <span class="stats-value">{{ memory_peak|default('N/A') }}</span>
                    </div>
                </div>
                
                <!-- 用户信息 -->
                <div class="info-card">
                    <h3>👤 用户信息</h3>
                    <div class="stats-row">
                        <span class="stats-label">IP地址：</span>
                        <span class="stats-value">{{ user_info.ip|default('Unknown') }}</span>
                    </div>
                    <div class="stats-row">
                        <span class="stats-label">操作系统：</span>
                        <span class="stats-value">{{ user_info.os|default('Unknown') }}</span>
                    </div>
                    <div class="stats-row">
                        <span class="stats-label">浏览器：</span>
                        <span class="stats-value">{{ user_info.browser|default('Unknown')|slice(0, 50) }}{% if user_info.browser|length > 50 %}...{% endif %}</span>
                    </div>
                    <div class="stats-row">
                        <span class="stats-label">请求URI：</span>
                        <span class="stats-value">{{ user_info.request_uri|default('Unknown') }}</span>
                    </div>
                </div>
            </div>

            <!-- 系统状态信息（如果提供） -->
            {% if system_status %}
            <div class="info-card">
                <h3>📊 系统状态</h3>
                {% for key, value in system_status %}
                <div class="stats-row">
                    <span class="stats-label">{{ key }}：</span>
                    <span class="stats-value">{{ value }}</span>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- 错误堆栈跟踪（如果提供） -->
            {% if error_trace %}
            <h3>🔍 错误堆栈跟踪</h3>
            <div class="trace-box">{{ error_trace }}</div>
            {% endif %}

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <a href="{{ base_url|default('http://pay.jiamisoft.com/') }}admin/logs" class="btn">查看完整日志</a>
                <a href="{{ base_url|default('http://pay.jiamisoft.com/') }}admin/system-status" class="btn btn-secondary">系统状态</a>
            </div>
        </div>
        
        <!-- 邮件底部 -->
        <div class="footer">
            <p><strong>{{ company_name|default('洛阳夏冰软件技术有限公司') }}</strong> - 系统监控报警</p>
            <p>此邮件由系统自动发送，请勿直接回复</p>
            <p>
                如需技术支持，请联系：
                <a href="mailto:{{ service_email|default('<EMAIL>') }}">{{ service_email|default('<EMAIL>') }}</a> 
                | {{ service_phone|default('************') }}
            </p>
            <div style="margin-top: 15px; font-size: 12px; color: #999;">
                发送时间：{{ send_time|default('now'|date('Y-m-d H:i:s')) }}
                {% if error_id %} | 错误ID：{{ error_id }}{% endif %}
            </div>
        </div>
    </div>
</body>
</html>