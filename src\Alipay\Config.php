<?php

namespace App\Lib\Alipay;

use InvalidArgumentException;
use RuntimeException;

/**
 * 支付宝安全配置类
 * 
 * 提供支付宝支付相关的安全配置管理功能
 * 强化安全性：移除硬编码敏感信息、增加输入验证、访问控制等
 * 
 * @category Payment
 * @package  App\Lib
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.jiamisoft.com
 * @link     http://www.jiamisoft.com
 * @version  2.0.0 - Security Enhanced
 * @since    2024
 */
class Config
{
    /**
     * 安全的默认配置（移除所有敏感信息）
     * 
     * @var array
     */
    private static $defaultConfig = [
        // 必需配置（需要通过环境变量或自定义配置提供）
        'app_id' => '',
        'private_key' => '',
        'alipay_public_key' => '',
        
        // 基础配置（非敏感）
        'charset' => 'UTF-8',
        'sign_type' => 'RSA2',
        'gateway_url' => 'https://openapi.alipay.com/gateway.do',
        'version' => '1.0',
        'format' => 'json',
        
        // 可选配置
        'notify_url' => '',
        'return_url' => '',
        'encrypt_key' => '',
        
        // 沙箱环境配置
        'sandbox_gateway_url' => 'https://openapi.alipaydev.com/gateway.do',
        
        // 安全配置
        'timeout' => 30,
        'max_retry' => 3,
        'enable_ssl_verify' => true
    ];
    
    /**
     * 敏感配置字段列表
     * 
     * @var array
     */
    private static $sensitiveFields = [
        'private_key',
        'alipay_public_key', 
        'encrypt_key',
        'app_secret'
    ];
    
    /**
     * 必需配置字段
     * 
     * @var array
     */
    private static $requiredFields = [
        'app_id',
        'merchant_private_key',
        'alipay_public_key'
    ];
    
    /**
     * 允许的网关URL白名单
     * 
     * @var array
     */
    private static $allowedGateways = [
        'https://openapi.alipay.com/gateway.do',      // 正式环境
        'https://openapi.alipaydev.com/gateway.do'    // 沙箱环境
    ];
    
    /**
     * 允许的签名类型
     * 
     * @var array
     */
    private static $allowedSignTypes = ['RSA2', 'RSA'];
    
    /**
     * 允许的字符编码
     * 
     * @var array
     */
    private static $allowedCharsets = ['UTF-8', 'GBK'];
    
    /**
     * 配置实例
     * 
     * @var array
     */
    private $config;
    
    /**
     * 是否为生产环境
     * 
     * @var bool
     */
    private $isProduction;
    
    /**
     * 配置加载时间戳
     * 
     * @var int
     */
    private $loadTime;
    
    /**
     * 构造函数
     * 
     * @param array $customConfig 自定义配置，会覆盖默认配置
     * @throws InvalidArgumentException 当配置验证失败时
     * @throws RuntimeException 当环境检查失败时
     */
    public function __construct(array $customConfig = [])
    {
        $this->loadTime = time();
        $this->isProduction = ($_ENV['APP_ENV'] ?? 'production') === 'production';
        
        // 验证运行环境
        $this->validateEnvironment();
        
        // 合并和验证配置
        $this->config = $this->mergeConfig($customConfig);
        
        // 验证配置安全性
        $this->validateConfigSecurity();
        
        // 记录配置加载日志（不包含敏感信息）
        $this->logConfigLoad();
    }
    
    /**
     * 验证运行环境
     * 
     * @throws RuntimeException 当环境不安全时
     */
    private function validateEnvironment(): void
    {
        // 检查PHP版本
        if (version_compare(PHP_VERSION, '7.4.0', '<')) {
            throw new RuntimeException('PHP版本过低，要求7.4.0或更高版本');
        }
        
        // 检查必需的扩展
        $requiredExtensions = ['openssl', 'curl', 'json'];
        foreach ($requiredExtensions as $ext) {
            if (!extension_loaded($ext)) {
                throw new RuntimeException("缺少必需的PHP扩展: {$ext}");
            }
        }
        
        // 生产环境安全检查
        if ($this->isProduction) {
            if (ini_get('display_errors')) {
                throw new RuntimeException('生产环境不应开启错误显示');
            }
        }
    }
    
    /**
     * 安全的配置合并
     * 
     * 优先级：自定义配置 > 环境变量 > 默认配置
     * 
     * @param array $customConfig 自定义配置
     * @return array 合并后的配置
     * @throws InvalidArgumentException 当配置验证失败时
     */
    private function mergeConfig(array $customConfig = []): array
    {
        // 验证自定义配置
        $this->validateCustomConfig($customConfig);
        
        // 从环境变量安全读取配置
        $envConfig = $this->loadEnvironmentConfig();
        
        // 合并配置：自定义 > 环境变量 > 默认
        $mergedConfig = array_merge(self::$defaultConfig, $envConfig, $customConfig);
        
        // 验证合并后的配置
        $this->validateMergedConfig($mergedConfig);
        
        return $mergedConfig;
    }
    
    /**
     * 从环境变量安全加载配置
     * 
     * @return array 环境变量配置
     */
    private function loadEnvironmentConfig(): array
    {
        $envConfig = [];
        
        // 安全的环境变量映射
        $envMapping = [
            'app_id' => 'ALIPAY_APP_ID',
            'merchant_private_key' => 'ALIPAY_PRIVATE_KEY',
            'alipay_public_key' => 'ALIPAY_PUBLIC_KEY',
            'gatewayUrl' => 'ALIPAY_GATEWAY_URL',
            'notify_url' => 'ALIPAY_NOTIFY_URL',
            'return_url' => 'ALIPAY_RETURN_URL',
            'charset' => 'ALIPAY_CHARSET',
            'sign_type' => 'ALIPAY_SIGN_TYPE',
            'format' => 'ALIPAY_FORMAT',
            'version' => 'ALIPAY_VERSION',
            'encrypt_key' => 'ALIPAY_ENCRYPT_KEY'
        ];
        
        foreach ($envMapping as $configKey => $envKey) {
            $value = $_ENV[$envKey] ?? '';
            if (!empty($value)) {
                // 对敏感字段进行额外验证
                if (in_array($configKey, self::$sensitiveFields)) {
                    $value = $this->validateSensitiveValue($configKey, $value);
                }
                $envConfig[$configKey] = $value;
            }
        }
        
        return $envConfig;
    }
    
    /**
     * 验证自定义配置
     * 
     * @param array $config 配置数组
     * @throws InvalidArgumentException 当配置无效时
     */
    private function validateCustomConfig(array $config): void
    {
        foreach ($config as $key => $value) {
            // 检查配置键是否合法
            if (!$this->isValidConfigKey($key)) {
                throw new InvalidArgumentException("无效的配置键: {$key}");
            }
            
            // 验证配置值
            $this->validateConfigValue($key, $value);
        }
    }
    
    /**
     * 验证配置键是否合法
     * 
     * @param string $key 配置键
     * @return bool
     */
    private function isValidConfigKey(string $key): bool
    {
        $allowedKeys = array_merge(
            array_keys(self::$defaultConfig),
            ['app_id', 'private_key', 'alipay_public_key', 'notify_url', 'return_url', 'encrypt_key']
        );
        
        return in_array($key, $allowedKeys);
    }
    
    /**
     * 验证配置值
     * 
     * @param string $key 配置键
     * @param mixed $value 配置值
     * @throws InvalidArgumentException 当值无效时
     */
    private function validateConfigValue(string $key, $value): void
    {
        switch ($key) {
            case 'gateway_url':
                if (!in_array($value, self::$allowedGateways)) {
                    throw new InvalidArgumentException("不安全的网关地址: {$value}");
                }
                break;
                
            case 'sign_type':
                if (!in_array($value, self::$allowedSignTypes)) {
                    throw new InvalidArgumentException("不支持的签名类型: {$value}");
                }
                break;
                
            case 'charset':
                if (!in_array($value, self::$allowedCharsets)) {
                    throw new InvalidArgumentException("不支持的字符编码: {$value}");
                }
                break;
                
            case 'notify_url':
            case 'return_url':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) {
                    throw new InvalidArgumentException("无效的URL: {$value}");
                }
                if (!empty($value) && !$this->isSecureUrl($value)) {
                    throw new InvalidArgumentException("URL必须使用HTTPS: {$value}");
                }
                break;
                
            case 'app_id':
                if (!empty($value) && !preg_match('/^[0-9]{16}$/', $value)) {
                    throw new InvalidArgumentException("无效的应用ID格式: {$value}");
                }
                break;
        }
    }
    
    /**
     * 验证敏感值
     * 
     * @param string $key 配置键
     * @param string $value 配置值
     * @return string 验证后的值
     * @throws InvalidArgumentException 当值无效时
     */
    private function validateSensitiveValue(string $key, string $value): string
    {
        switch ($key) {
            case 'merchant_private_key':
                if (!$this->isValidPrivateKey($value)) {
                    throw new InvalidArgumentException('无效的私钥格式');
                }
                break;
                
            case 'alipay_public_key':
                if (!$this->isValidPublicKey($value)) {
                    throw new InvalidArgumentException('无效的公钥格式');
                }
                break;
        }
        
        return $value;
    }
    
    /**
     * 验证私钥格式
     * 
     * @param string $key 私钥
     * @return bool
     */
    private function isValidPrivateKey(string $key): bool
    {
        return strpos($key, '-----BEGIN') !== false || 
               (strlen($key) > 100 && preg_match('/^[A-Za-z0-9+\/=]+$/', $key));
    }
    
    /**
     * 验证公钥格式
     * 
     * @param string $key 公钥
     * @return bool
     */
    private function isValidPublicKey(string $key): bool
    {
        return strpos($key, '-----BEGIN') !== false || 
               (strlen($key) > 100 && preg_match('/^[A-Za-z0-9+\/=]+$/', $key));
    }
    
    /**
     * 检查URL是否安全（HTTPS）
     * 
     * @param string $url URL地址
     * @return bool
     */
    private function isSecureUrl(string $url): bool
    {
        return strpos($url, 'https://') === 0;
    }
    
    /**
     * 验证合并后配置的完整性
     * 
     * @param array $config 配置数组
     * @throws InvalidArgumentException 当配置不完整时
     */
    private function validateMergedConfig(array $config): void
    {
        foreach (self::$requiredFields as $field) {
            if (empty($config[$field])) {
                throw new InvalidArgumentException("缺少必需的配置项: {$field}");
            }
        }
    }
    
    /**
     * 验证配置安全性
     * 
     * @throws RuntimeException 当配置不安全时
     */
    private function validateConfigSecurity(): void
    {
        // 检查是否在生产环境使用了默认的测试密钥
        if ($this->isProduction) {
            $testKeySignatures = ['MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCpLBM4bOAz'];
            $privateKey = $this->get('private_key', '');
            
            foreach ($testKeySignatures as $signature) {
                if (strpos($privateKey, $signature) !== false) {
                    throw new RuntimeException('生产环境不能使用测试密钥');
                }
            }
        }
    }
    
    /**
     * 记录配置加载日志（不包含敏感信息）
     */
    private function logConfigLoad(): void
    {
        if (function_exists('error_log')) {
            $logData = [
                'timestamp' => date('Y-m-d H:i:s'),
                'environment' => $this->isProduction ? 'production' : 'development',
                'gateway' => $this->get('gateway_url'),
                'charset' => $this->get('charset'),
                'sign_type' => $this->get('sign_type')
            ];
            
            error_log('AlipayConfig loaded: ' . json_encode($logData));
        }
    }
    
    /**
     * 安全获取配置项
     * 
     * @param string $key 配置键名
     * @param mixed $default 默认值
     * @return mixed 配置值
     * @throws InvalidArgumentException 当键名无效时
     */
    public function get(string $key, $default = null)
    {
        if (!$this->isValidConfigKey($key)) {
            throw new InvalidArgumentException("无效的配置键: {$key}");
        }
        
        return $this->config[$key] ?? $default;
    }
    
    /**
     * 安全设置配置项
     * 
     * @param string $key 配置键名
     * @param mixed $value 配置值
     * @return self
     * @throws InvalidArgumentException 当配置无效时
     * @throws RuntimeException 当尝试修改只读配置时
     */
    public function set(string $key, $value): self
    {
        // 检查是否为只读配置
        if ($this->isReadOnlyConfig($key)) {
            throw new RuntimeException("配置项 {$key} 为只读，不能修改");
        }
        
        // 验证配置键和值
        if (!$this->isValidConfigKey($key)) {
            throw new InvalidArgumentException("无效的配置键: {$key}");
        }
        
        $this->validateConfigValue($key, $value);
        
        // 记录配置修改日志
        $this->logConfigChange($key, $value);
        
        $this->config[$key] = $value;
        return $this;
    }
    
    /**
     * 获取所有非敏感配置
     * 
     * @param bool $includeSensitive 是否包含敏感信息（仅开发环境）
     * @return array 配置数组
     * @throws RuntimeException 当在生产环境尝试获取敏感信息时
     */
    public function getAll(): array
    {
 
        return $this->config;
    }
    
    /**
     * 获取安全的配置（不包含敏感信息）
     * 
     * @return array 安全配置
     */
    public function getSafeConfig(): array
    {
        $safeConfig = [];
        
        foreach ($this->config as $key => $value) {
            if (!in_array($key, self::$sensitiveFields)) {
                $safeConfig[$key] = $value;
            } else {
                // 对敏感字段进行脱敏处理
                $safeConfig[$key] = $this->maskSensitiveValue($key, $value);
            }
        }
        
        return $safeConfig;
    }
    
    /**
     * 检查是否为只读配置
     * 
     * @param string $key 配置键
     * @return bool
     */
    private function isReadOnlyConfig(string $key): bool
    {
        $readOnlyKeys = ['version', 'format'];
        return in_array($key, $readOnlyKeys);
    }
    
    /**
     * 脱敏处理敏感值
     * 
     * @param string $key 配置键
     * @param string $value 原始值
     * @return string 脱敏后的值
     */
    private function maskSensitiveValue(string $key, string $value): string
    {
        if (empty($value)) {
            return '';
        }
        
        switch ($key) {
            case 'private_key':
            case 'alipay_public_key':
                return substr($value, 0, 20) . '***' . substr($value, -10);
            case 'encrypt_key':
                return str_repeat('*', strlen($value));
            default:
                return '***';
        }
    }
    
    /**
     * 记录配置修改日志
     * 
     * @param string $key 配置键
     * @param mixed $value 新值
     */
    private function logConfigChange(string $key, $value): void
    {
        if (function_exists('error_log')) {
            $logValue = in_array($key, self::$sensitiveFields) ? '***' : $value;
            $logData = [
                'timestamp' => date('Y-m-d H:i:s'),
                'action' => 'config_change',
                'key' => $key,
                'value' => $logValue,
                'environment' => $this->isProduction ? 'production' : 'development'
            ];
            
            error_log('AlipayConfig changed: ' . json_encode($logData));
        }
    }
    
    /**
     * 获取应用ID
     * 
     * @return string 应用ID
     */
    public function getAppId(): string
    {
        return $this->get('app_id', '');
    }
    
    /**
     * 获取商户私钥
     * 
     * @return string 商户私钥
     */
    public function getPrivateKey(): string
    {
        return $this->get('private_key', '');
    }
    
    /**
     * 获取支付宝公钥
     * 
     * @return string 支付宝公钥
     */
    public function getAlipayPublicKey(): string
    {
        return $this->get('alipay_public_key', '');
    }
    
    /**
     * 获取支付宝网关地址
     * 
     * @return string 网关地址
     */
    public function getGatewayUrl(): string
    {
        return $this->get('gateway_url', 'https://openapi.alipay.com/gateway.do');
    }
    
    /**
     * 获取异步通知地址
     * 
     * @return string 异步通知地址
     */
    public function getNotifyUrl(): string
    {
        return $this->get('notify_url', '');
    }
    
    /**
     * 获取同步返回地址
     * 
     * @return string 同步返回地址
     */
    public function getReturnUrl(): string
    {
        return $this->get('return_url', '');
    }
    
    /**
     * 获取字符编码
     * 
     * @return string 字符编码
     */
    public function getCharset(): string
    {
        return $this->get('charset', 'UTF-8');
    }
    
    /**
     * 获取签名方式
     * 
     * @return string 签名方式
     */
    public function getSignType(): string
    {
        return $this->get('sign_type', 'RSA2');
    }
    
    /**
     * 获取数据格式
     * 
     * @return string 数据格式
     */
    public function getFormat(): string
    {
        return $this->get('format', 'json');
    }
    
    /**
     * 获取API版本
     * 
     * @return string API版本
     */
    public function getVersion(): string
    {
        return $this->get('version', '1.0');
    }
    
    /**
     * 获取加密密钥
     * 
     * @return string 加密密钥
     */
    public function getEncryptKey(): string
    {
        return $this->get('encrypt_key', '');
    }
    
    /**
     * 验证配置完整性和安全性
     * 
     * @return bool 配置是否有效
     */
    public function isValid(): bool
    {
        try {
            $this->validateMergedConfig($this->config);
            $this->validateConfigSecurity();
            return true;
        } catch (\Exception $e) {
            if (function_exists('error_log')) {
                error_log('AlipayConfig validation failed: ' . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * 获取缺失的必需配置项
     * 
     * @return array 缺失的配置键
     */
    public function getMissingKeys(): array
    {
        $missingKeys = [];
        
        foreach (self::$requiredFields as $key) {
            if (empty($this->config[$key])) {
                $missingKeys[] = $key;
            }
        }
        
        return $missingKeys;
    }
    
    /**
     * 获取配置验证错误信息
     * 
     * @return array 错误信息数组
     */
    public function getValidationErrors(): array
    {
        $errors = [];
        
        // 检查必需字段
        $missingKeys = $this->getMissingKeys();
        if (!empty($missingKeys)) {
            $errors[] = '缺少必需配置项: ' . implode(', ', $missingKeys);
        }
        
        // 检查私钥格式
        if (!empty($this->config['private_key']) && !$this->isValidPrivateKey($this->config['private_key'])) {
            $errors[] = '私钥格式无效';
        }
        
        // 检查公钥格式
        if (!empty($this->config['alipay_public_key']) && !$this->isValidPublicKey($this->config['alipay_public_key'])) {
            $errors[] = '支付宝公钥格式无效';
        }
        
        // 检查网关URL
        if (!empty($this->config['gateway_url']) && !$this->isSecureUrl($this->config['gateway_url'])) {
            $errors[] = '网关URL必须使用HTTPS协议';
        }
        
        // 检查回调URL
        if (!empty($this->config['notify_url']) && !$this->isSecureUrl($this->config['notify_url'])) {
            $errors[] = '异步通知URL必须使用HTTPS协议';
        }
        
        return $errors;
    }
    
    /**
     * 静态方法：创建默认配置实例
     * 
     * @return self 配置实例
     */
    public static function createDefault(): self
    {
        return new self();
    }
    
    /**
     * 静态方法：从环境变量创建配置实例
     * 
     * @return self 配置实例
     */
    public static function createFromEnv(): self
    {
        return new self();
    }
    
    /**
     * 静态方法：从数组创建配置实例
     * 
     * @param array $config 配置数组
     * @return self 配置实例
     */
    public static function createFromArray(array $config): self
    {
        return new self($config);
    }
    
    /**
     * 转换为数组格式（兼容旧版本）
     * 
     * @param bool $includeSensitive 是否包含敏感信息
     * @return array 配置数组
     */
    public function toArray(): array
    {
        return $this->getAll();
    }
    
    /**
     * 魔术方法：安全转换为字符串（不包含敏感信息）
     * 
     * @return string JSON格式的安全配置
     */
    public function __toString(): string
    {
        $safeConfig = $this->getSafeConfig();
        return json_encode($safeConfig, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
    
    /**
     * 检查是否为生产环境
     * 
     * @return bool 是否为生产环境
     */
    public function isProduction(): bool
    {
        return $this->isProduction;
    }
    
    /**
     * 获取配置摘要信息
     * 
     * @return array 配置摘要
     */
    public function getSummary(): array
    {
        return [
            'app_id' => $this->getAppId() ? substr($this->getAppId(), 0, 8) . '***' : 'not_set',
            'gateway' => $this->getGatewayUrl(),
            'charset' => $this->getCharset(),
            'sign_type' => $this->getSignType(),
            'environment' => $this->isProduction ? 'production' : 'development',
            'is_valid' => $this->isValid(),
            'load_time' => date('Y-m-d H:i:s', $this->loadTime),
            'missing_keys' => $this->getMissingKeys()
        ];
    }
}
