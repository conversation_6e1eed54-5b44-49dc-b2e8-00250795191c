<?php
namespace App\Lib\Alipay;

use Monolog\Logger;

require_once __DIR__ . '/../../ali/service/AlipayTradeService.php';
require_once __DIR__ . '/../../ali/buildermodel/AlipayTradePagePayContentBuilder.php';

class AliPay
{
    private $payRequestBuilder;
    private $payResponse;
    private $logger;
    private $return_url;
    private $notify_url;
    private $config;

    public function __construct( array $order_info)
    {

        $this->validateOrderInfo($order_info);
        $this->initializeLogger();

        try {
            $this->payRequestBuilder = new \AlipayTradePagePayContentBuilder();
            $this->payRequestBuilder->setBody($order_info['body']);
            $this->payRequestBuilder->setSubject($order_info['subject']);
            $this->payRequestBuilder->setOutTradeNo($order_info['out_trade_no']);
            $this->payRequestBuilder->setTotalAmount($order_info['total_amount']);
            $this->payRequestBuilder->setTimeExpress($order_info['timeout_express']);
            $this->return_url = $order_info['return_url'];
            $this->notify_url = $order_info['notify_url'];
            $this->config = $order_info['config'];

            $this->payResponse = new \AlipayTradeService($this->config);

          
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }

    private function validateOrderInfo(array $order_info): void
    {
        $required = ['body', 'subject', 'out_trade_no', 'total_amount', 'timeout_express', 'return_url', 'notify_url', 'config'];

        foreach ($required as $key) {
            if (!isset($order_info[$key]) || $order_info[$key] === '') {
                throw new \InvalidArgumentException("缺少必需参数: {$key}");
            }
        }

        // 验证金额格式
        if (!is_numeric($order_info['total_amount']) || $order_info['total_amount'] <= 0) {
            throw new \InvalidArgumentException("total_amount 必须是正数");
        }
    }

    private function initializeLogger()
    {
        try{
             $logDir = $_ENV['LOG_PATH'] . '/ali/error/';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0750, true);
        }
        $this->logger = new Logger('ali_pay');
       
        $this->logger->pushHandler(new \Monolog\Handler\RotatingFileHandler($logDir . 'error.log', 0, Logger::INFO));
        }catch(\Exception $e){
            throw new \Exception($e->getMessage());
        }
    }

    public function pay()
    {
        if ($this->payResponse === null) {
            throw new \RuntimeException('支付服务未正确初始化');
        }

        try {
            $response = $this->payResponse->pagePay($this->payRequestBuilder, $this->return_url, $this->notify_url);
            return $response;
        } catch (\Exception $e) {
            $this->logger->error('支付处理失败: ' . $e->getMessage());
            throw new \RuntimeException('支付处理失败: ' . $e->getMessage(), 0, $e);
        }
    }
}