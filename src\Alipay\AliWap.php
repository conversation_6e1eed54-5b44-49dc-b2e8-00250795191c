<?php

namespace App\Lib\Alipay;

use Monolog\Logger;
use InvalidArgumentException;
use RuntimeException;

require_once __DIR__ . '/../../alipay.wap/wappay/service/AlipayTradeService.php';
require_once __DIR__ . '/../../alipay.wap/wappay/buildermodel/AlipayTradeWapPayContentBuilder.php';

class AliWap
{
    private $payRequestBuilder;
    private $payResponse;
    private $logger;
    private $return_url;
    private $notify_url;
    private $config;

    public function __construct(array $order_info)
    {
        $this->logger = new Logger('ali_wap');  // 先初始化 Logger 实例
        $logDir = $_ENV['LOG_PATH'] . '/ali/error/';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0750, true);
        }
        $this->logger->pushHandler(new \Monolog\Handler\RotatingFileHandler($logDir . 'error.log', 0, Logger::DEBUG));

        try {
            $this->payRequestBuilder = new \AlipayTradeWapPayContentBuilder();
            $this->payRequestBuilder->setBody($order_info['body']);
            $this->payRequestBuilder->setSubject($order_info['subject']);
            $this->payRequestBuilder->setOutTradeNo($order_info['out_trade_no']);
            $this->payRequestBuilder->setTotalAmount($order_info['total_amount']);
            $this->payRequestBuilder->setTimeExpress($order_info['timeout_express']);
            $this->return_url = $order_info['return_url'];
            $this->notify_url = $order_info['notify_url'];
            $this->config = $order_info['config'];

            $this->payResponse = new \AlipayTradeService($this->config);         
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }

    public function pay()
    {
        try {
            $response = $this->payResponse->wapPay($this->payRequestBuilder, $this->return_url, $this->notify_url);
            return $response;
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }
}