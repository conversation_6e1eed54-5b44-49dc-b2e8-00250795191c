<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>邮件发送 Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background: #f5f5f7;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
        }
        .btn {
            display: inline-block;
            margin: 10px;
            padding: 12px 24px;
            background: #007aff;
            color: #fff;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .btn:hover {
            background: #0051d5;
        }
        #msg {
            margin-top: 20px;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <button class="btn" id="sendActivation">发送授权码邮件</button>
    <button class="btn" id="sendError">发送系统错误报警</button>
    <div id="msg"></div>

    <script>
        function sendMail(type) {
            const msg = document.getElementById('msg');
            msg.textContent = '发送中…';
            msg.style.color = '#333';

            fetch('send_mail_api.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'type=' + encodeURIComponent(type),
            })
                .then((res) => res.json())
                .then((data) => {
                    if (data.success) {
                        msg.textContent = data.message;
                        msg.style.color = '#16a34a';
                    } else {
                        msg.textContent = data.message;
                        msg.style.color = '#dc2626';
                    }
                })
                .catch((err) => {
                    msg.textContent = '请求失败：' + err;
                    msg.style.color = '#dc2626';
                });
        }

        document.getElementById('sendActivation').addEventListener('click', () => sendMail('activation'));
        document.getElementById('sendError').addEventListener('click', () => sendMail('error'));
    </script>
</body>
</html>
