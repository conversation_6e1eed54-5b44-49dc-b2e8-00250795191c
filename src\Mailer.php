<?php

declare(strict_types=1);

namespace App\Lib;

use PHPMailer\PHPMailer\PHPMailer;
use P<PERSON><PERSON>ailer\PHPMailer\Exception as PHPMailerException;
use Monolog\Logger;

/**
 * 邮件发送封装类，基于 PHPMailer。
 *
 * 环境变量配置示例：
 *   MAIL_HOST=smtp.qq.com
 *   MAIL_PORT=465
 *   MAIL_ENCRYPTION=ssl   # ssl / tls / 空字符串
 *   MAIL_USERNAME=<EMAIL>
 *   MAIL_PASSWORD=your_password_or_token
 *   MAIL_FROM_ADDRESS=<EMAIL>
 *   MAIL_FROM_NAME=JiamiSoft
 *   MAIL_DEBUG=false
 *   MAIL_CHARSET=UTF-8
 */
class Mailer
{
    /** @var PHPMailer */
    private PHPMailer $mailer;

    /** @var Logger|null */
    private ?Logger $logger;

    public function __construct(?Logger $logger = null)
    {
        $this->logger = $logger;

        $this->mailer = new PHPMailer(true);
        $this->configure();
    }

    /**
     * 读取 .env 并初始化 PHPMailer。
     */
    private function configure(): void
    {
        // 在初始化前校验必要的环境变量，避免运行期抛出低可读性的异常
        $this->assertRequiredEnvironment();

        // 基础配置
        $this->mailer->isSMTP();
        $this->mailer->Host       = $_ENV['MAIL_HOST'] ?? 'localhost';
        $this->mailer->Port       = (int) ($_ENV['MAIL_PORT'] ?? 25);
        $encryption = strtolower($_ENV['MAIL_ENCRYPTION'] ?? '');
        if ($encryption === 'ssl' || $encryption === 'tls') {
            $this->mailer->SMTPSecure = $encryption;
        } else {
            // 禁用自动 TLS，避免 STARTTLS 失败
            $this->mailer->SMTPAutoTLS = false;
        }
        $this->mailer->SMTPAuth   = true;
        $this->mailer->Username   = $_ENV['MAIL_USERNAME'] ?? '';
        $this->mailer->Password   = $_ENV['MAIL_PASSWORD'] ?? '';

        // 编码与发件人
        $this->mailer->CharSet = $_ENV['MAIL_CHARSET'] ?? 'UTF-8';
        $fromAddress = $this->resolveFromAddress();
        $fromName    = $_ENV['MAIL_FROM_NAME'] ?? '';
        $this->mailer->setFrom($fromAddress, $fromName);

        // 调试
        $debug = ($_ENV['MAIL_DEBUG'] ?? 'false') === 'true';
        $this->mailer->SMTPDebug = $debug ? 2 : 0; // PHPMailer::DEBUG_SERVER = 2
        if ($debug && $this->logger instanceof Logger) {
            // 将调试输出写入 Monolog
            $this->mailer->Debugoutput = function ($str, $level) {
                // phpcs:ignore Generic.CodeAnalysis.UnusedFunctionParameter -- $level 目前无用
                if ($this->logger instanceof Logger) {
                    $this->logger->debug('[PHPMailer] ' . trim($str));
                }
            };
        }
    }

    /**
     * 校验必要的邮件环境变量是否存在且合法。
     *
     * @throws MailException
     */
    private function assertRequiredEnvironment(): void
    {
        $missingKeys = [];

        if (empty($_ENV['MAIL_HOST'])) {
            $missingKeys[] = 'MAIL_HOST';
        }
        // 端口需要为有效正整数
        $port = (int) ($_ENV['MAIL_PORT'] ?? 0);
        if ($port <= 0 || $port > 65535) {
            $missingKeys[] = 'MAIL_PORT';
        }
        if (empty($_ENV['MAIL_USERNAME'])) {
            $missingKeys[] = 'MAIL_USERNAME';
        }
        // 密码允许为空字符串？通常不允许，这里按必填处理
        if (!isset($_ENV['MAIL_PASSWORD']) || $_ENV['MAIL_PASSWORD'] === '') {
            $missingKeys[] = 'MAIL_PASSWORD';
        }

        if (!empty($missingKeys)) {
            throw new MailException('邮件环境变量缺失/非法: ' . implode(', ', $missingKeys));
        }

        // 发件人地址必须是合法邮箱（来自 MAIL_FROM_ADDRESS 或回落至 MAIL_USERNAME）
        $fromAddress = $_ENV['MAIL_FROM_ADDRESS'] ?? $_ENV['MAIL_USERNAME'] ?? '';
        if (!filter_var($fromAddress, FILTER_VALIDATE_EMAIL)) {
            throw new MailException('发件人地址无效，请配置 MAIL_FROM_ADDRESS 或 MAIL_USERNAME 为合法邮箱');
        }
    }

    /**
     * 解析并返回合法的发件人邮箱。
     */
    private function resolveFromAddress(): string
    {
        $fromAddress = $_ENV['MAIL_FROM_ADDRESS'] ?? $_ENV['MAIL_USERNAME'] ?? '';
        return (string) $fromAddress;
    }

    /**
     * 发送邮件。
     *
     * @param string|array        $to           收件人，形如 "<EMAIL>" 或 ["<EMAIL>" => "张三"]
     * @param string              $subject      主题
     * @param string              $body         邮件正文（默认 HTML）
     * @param array<int,array>    $attachments  附件，如 [['path' => '/tmp/a.pdf', 'name' => '合同.pdf']]
     * @param bool                $isHtml       是否 HTML 格式
     *
     * @throws MailException
     */
    public function send($to, string $subject, string $body, array $attachments = [], bool $isHtml = true): void
    {
        try {
            // 清理上一次的收件人等信息
            $this->mailer->clearAllRecipients();
            $this->mailer->clearAttachments();

            // 收件人
            if (is_array($to)) {
                foreach ($to as $address => $name) {
                    // 支持纯数组形式 [0=>'a@b']
                    if (is_int($address)) {
                        $this->mailer->addAddress($name);
                    } else {
                        $this->mailer->addAddress($address, (string) $name);
                    }
                }
            } else {
                $this->mailer->addAddress($to);
            }

            // 主题/正文
            $this->mailer->Subject = $subject;
            if ($isHtml) {
                $this->mailer->isHTML(true);
                $this->mailer->Body    = $body;
                $this->mailer->AltBody = strip_tags($body);
            } else {
                $this->mailer->isHTML(false);
                $this->mailer->Body = $body;
            }

            // 附件
            foreach ($attachments as $att) {
                if (!isset($att['path'])) {
                    continue;
                }
                $name = $att['name'] ?? '';
                $this->mailer->addAttachment($att['path'], $name);
            }

            // 发送
            $this->mailer->send();
        } catch (PHPMailerException $e) {
            if ($this->logger instanceof Logger) {
                $this->logger->error('邮件发送失败: ' . $e->getMessage());
            }
            $errorInfo = $this->mailer->ErrorInfo ?? '';
            throw new MailException($e->getMessage(), (int) $e->getCode(), $e, $errorInfo);
        }
    }

    /**
     * 设置回复地址。
     */
    public function setReplyTo(string $address, string $name = ''): self
    {
        $this->mailer->ClearReplyTos();
        $this->mailer->addReplyTo($address, $name);
        return $this;
    }

    /**
     * 添加抄送。
     */
    public function addCc(string $address, string $name = ''): self
    {
        $this->mailer->addCC($address, $name);
        return $this;
    }

    /**
     * 添加密送。
     */
    public function addBcc(string $address, string $name = ''): self
    {
        $this->mailer->addBCC($address, $name);
        return $this;
    }

    /**
     * 添加附件（链式）。
     */
    public function addAttachment(string $filePath, string $name = ''): self
    {
        $this->mailer->addAttachment($filePath, $name);
        return $this;
    }
}
