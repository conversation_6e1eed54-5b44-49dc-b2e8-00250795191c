<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ company_name }} - 软件授权码通知</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.8;
            color: #1a1a1a;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            font-weight: 400;
        }

        .email-container {
            max-width: 680px;
            margin: 40px auto;
            background: #ffffff;
            border: 1px solid #e2e8f0;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #f8fafc;
            padding: 40px 30px;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #3b82f6, #06b6d4, #10b981);
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 12px;
            font-weight: 300;
            letter-spacing: -0.5px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.8;
            font-weight: 300;
        }

        .content {
            padding: 50px 40px;
        }

        .document-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e2e8f0;
        }

        .document-title {
            font-size: 24px;
            font-weight: 300;
            color: #0f172a;
            margin-bottom: 8px;
            letter-spacing: -0.3px;
        }

        .document-subtitle {
            font-size: 14px;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .greeting {
            font-size: 16px;
            margin-bottom: 30px;
            color: #334155;
            line-height: 1.6;
        }

        .formal-notice {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border-left: 3px solid #3b82f6;
            padding: 25px 30px;
            margin: 30px 0;
            border-radius: 0 8px 8px 0;
        }

        .activation-section {
            background: #f8fafc;
            border: 1px solid #cbd5e1;
            border-radius: 12px;
            padding: 40px 30px;
            margin: 40px 0;
            text-align: center;
            position: relative;
        }

        .activation-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #06b6d4);
            border-radius: 0 0 3px 3px;
        }

        .activation-label {
            font-size: 14px;
            color: #475569;
            margin-bottom: 20px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .activation-code {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 20px;
            font-weight: 600;
            color: #0f172a;
            background: #ffffff;
            padding: 20px 30px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            letter-spacing: 3px;
            word-break: break-all;
            margin: 15px 0;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .copy-hint {
            font-size: 13px;
            color: #64748b;
            margin-top: 15px;
            font-style: italic;
        }

        .download-section {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border: 1px solid #cbd5e1;
            border-radius: 12px;
            padding: 30px;
            margin: 30px 0;
        }

        .download-title {
            font-size: 18px;
            color: #0f172a;
            margin-bottom: 15px;
            font-weight: 300;
            letter-spacing: -0.2px;
        }

        .download-link {
            display: inline-block;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            letter-spacing: 0.5px;
        }

        .download-link:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
            transform: translateY(-1px);
            box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.15);
        }

        .support-info {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 30px;
            margin: 40px 0;
        }

        .support-title {
            font-size: 18px;
            color: #0f172a;
            margin-bottom: 25px;
            font-weight: 300;
            letter-spacing: -0.2px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e2e8f0;
        }

        .contact-item {
            margin: 15px 0;
            font-size: 15px;
            color: #475569;
            display: flex;
            align-items: center;
        }

        .contact-item strong {
            color: #0f172a;
            min-width: 100px;
            font-weight: 500;
        }

        .qr-section {
            text-align: center;
            margin: 30px 0;
            padding: 25px;
            background: #f8fafc;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
        }

        .qr-image {
            max-width: 160px;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .footer {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #94a3b8;
            padding: 40px 30px;
            text-align: center;
            font-size: 14px;
            position: relative;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, #3b82f6, #06b6d4, #10b981);
        }

        .footer-logo {
            font-size: 20px;
            color: #f8fafc;
            margin-bottom: 15px;
            font-weight: 300;
            letter-spacing: -0.3px;
        }

        .footer-links {
            margin: 20px 0;
        }

        .footer-links a {
            color: #64748b;
            text-decoration: none;
            margin: 0 15px;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: #f8fafc;
        }

        .timestamp {
            margin-top: 20px;
            font-size: 12px;
            color: #64748b;
            font-style: italic;
        }

        @media (max-width: 680px) {
            .email-container {
                margin: 20px 10px;
                border-radius: 0;
            }

            .content {
                padding: 30px 20px;
            }

            .activation-section {
                padding: 30px 20px;
            }

            .activation-code {
                font-size: 18px;
                padding: 15px 20px;
                letter-spacing: 2px;
            }

            .contact-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .contact-item strong {
                min-width: auto;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- 邮件头部 -->
        <div class="header">
            <h1>软件激活码通知</h1>
            <p>{{ company_name }} 数字化解决方案</p>
        </div>

        <!-- 邮件内容 -->
        <div class="content">
            <!-- 文档标题 -->
            <div class="document-header">
                <div class="document-title">软件产品激活通知书</div>
                <div class="document-subtitle">Software Product Activation Notice</div>
            </div>

            <div class="greeting">
                尊敬的用户：
            </div>

            <div class="formal-notice">
                <strong>购买确认通知</strong><br>
                感谢您选择 {{ company_name }} 的数字化解决方案。您已成功购买软件产品 <strong>{{ software_name }}</strong>，现将相关激活信息通知如下：
            </div>

            <!-- 激活码区域 -->
            <div class="activation-section">
                <div class="activation-label">软件注册码</div>
                <div class="activation-code">{{ activation_code }}</div>
                <div class="copy-hint">请将上述注册码复制至软件激活界面完成产品激活</div>
            </div>

            <!-- 下载区域 -->
            {% if download_url %}
            <div class="download-section">
                <div class="download-title">软件下载服务</div>
                <p style="margin-bottom: 20px; color: #475569; line-height: 1.6;">请通过以下链接获取软件最新版本：</p>
                <a href="{{ download_url }}" class="download-link">获取软件</a>
            </div>
            {% endif %}

            <!-- 客服信息 -->
            <div class="support-info">
                <div class="support-title">技术支持与客户服务</div>
                <div class="contact-item">
                    <strong>服务热线：</strong>{{ service_phone|default('************') }}
                </div>
                <div class="contact-item">
                    <strong>服务邮箱：</strong>{{ service_email|default('<EMAIL>') }}
                </div>
                <div class="contact-item">
                    <strong>服务时间：</strong>{{ work_time|default('工作日 9:00 - 12:00  14:00 - 17:30') }}
                </div>
                <div class="contact-item">
                    <strong>官方网站：</strong><a href="{{ website_url|default('https://www.jiamisoft.com/') }}" style="color: #3b82f6; text-decoration: none;">{{ website_url|default('https://www.jiamisoft.com/') }}</a>
                </div>
            </div>

            <!-- 企业微信二维码 -->
            {% if qr_code_url %}
            <div class="qr-section">
                <p style="margin-bottom: 15px; color: #475569; font-weight: 500;">企业微信客服</p>
                <img src="{{ qr_code_url }}" alt="企业微信客服" class="qr-image">
            </div>
            {% endif %}

            <p style="margin-top: 40px; color: #64748b; font-size: 15px; line-height: 1.6; text-align: center; font-style: italic;">
                {{ company_name }} 致力于为您提供专业的技术支持与优质的客户服务体验
            </p>
        </div>

        <!-- 邮件底部 -->
        <div class="footer">
            <div class="footer-logo">{{ company_name }}</div>
            <div>数字化转型 · 智能化未来</div>

            <div class="footer-links">
                <a href="{{ website_url|default('https://www.jiamisoft.com/') }}">官方网站</a>
                <a href="mailto:{{ service_email|default('<EMAIL>') }}">技术支持</a>
                {% if order_number %}
                <span style="color: #64748b;">订单编号：{{ order_number }}</span>
                {% endif %}
            </div>

            <div class="timestamp">
                系统发送时间：{{ send_time|default('now'|date('Y-m-d H:i:s')) }}
            </div>
        </div>
    </div>
</body>
</html>