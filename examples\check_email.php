<?php
// examples/check_email.php
// 用途：在浏览器中测试发送两类邮件（授权码/系统错误报警）
// 访问方式：/examples/check_email.php

declare(strict_types=1);

require_once __DIR__ . '/../boot.php';

use App\Lib\Mailer;
use App\Lib\Common;
use Monolog\Logger;
use Monolog\Handler\StreamHandler;

// 统一较短超时，避免阻塞
@set_time_limit(20);

// 简易模板渲染（支持 {{ key }} 与 {{key}}，可用点号路径如 user_info.ip）
function render_template(string $tpl, array $vars): string
{
    foreach ($vars as $k => $v) {
        $tpl = str_replace(['{{ ' . $k . ' }}', '{{' . $k . '}}'], (string) $v, $tpl);
    }
    return $tpl;
}

// JSON 响应快捷函数
function json_response(array $data, int $status = 200): void
{
    http_response_code($status);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// 处理 AJAX 发送
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $type = $_POST['type'] ?? '';

    if (!in_array($type, ['activation', 'error'], true)) {
        json_response(['success' => false, 'message' => '无效的类型'], 400);
    }

    // 收件人：ADM_EMAIL 必填
    $to = $_ENV['ADM_EMAIL'] ?? '';
    if (!filter_var($to, FILTER_VALIDATE_EMAIL)) {
        json_response(['success' => false, 'message' => '收件人邮箱未配置或不合法（ADM_EMAIL）'], 500);
    }

    $logger = new Logger('check_email');
    $logger->pushHandler(new StreamHandler('php://stderr', Logger::INFO));

    try {
        $mailer = new Mailer($logger);

        // 初始化 Twig（运行时检查，避免静态分析错误）
        $tmplDir = realpath(__DIR__ . '/../templates/email') ?: (__DIR__ . '/../templates/email');
        if (!(class_exists('Twig\\Environment') && class_exists('Twig\\Loader\\FilesystemLoader'))) {
            json_response(['success' => false, 'message' => 'Twig 未安装或未加载，请执行 composer install 并确保自动加载可用'], 500);
        }
        $loaderClass = 'Twig\\Loader\\FilesystemLoader';
        $envClass = 'Twig\\Environment';
        /** @var object $loader */
        $loader = new $loaderClass($tmplDir);
        /** @var object $twig */
        $twig = new $envClass($loader, [
            'cache' => false,
            'autoescape' => 'html',
            'strict_variables' => false,
        ]);

        if (method_exists($loader, 'exists')) {
            // ok
        }

        if ($type === 'activation') {
            if (method_exists($loader, 'exists') && !$loader->exists('activation_code.twig')) {
                json_response(['success' => false, 'message' => '找不到授权码模板'], 500);
            }
            $vars = [
                'company_name'     => '洛阳夏冰软件技术有限公司',
                'software_name'    => '测试软件',
                'activation_code'  => 'ABCD-EFGH-IJKL-MNOP',
                'download_url'     => 'https://www.jiamisoft.com/download.html',
                'service_phone'    => '************',
                'service_email'    => '<EMAIL>',
                'work_time'        => '工作日 9:00 - 12:00  14:00 - 17:30',
                'website_url'      => 'https://www.jiamisoft.com/',
                'order_number'     => 'TEST' . date('YmdHis'),
                'send_time'        => date('Y-m-d H:i:s'),
                'qr_code_url'      => '',
            ];
            /** @var callable $render */
            $body = $twig->render('activation_code.twig', $vars);
            $mailer->send($to, '【测试】软件授权码', $body, [], true);
            json_response(['success' => true, 'message' => '授权码测试邮件已发送至 ' . $to]);
        }

        // 系统错误报警
        if (method_exists($loader, 'exists') && !$loader->exists('system_error_alert.twig')) {
            json_response(['success' => false, 'message' => '找不到系统错误模板'], 500);
        }
        $vars = [
            'error_title'   => '数据库连接异常',
            'error_message' => 'PDOException: Access denied for user',
            'error_file'    => __FILE__,
            'error_line'    => 0,
            'error_time'    => date('Y-m-d H:i:s'),
            'error_level'   => 'CRITICAL',
            'error_color'   => '#dc3545',
            'company_name'  => '洛阳夏冰软件技术有限公司',
            'server_info'   => php_uname(),
            'php_version'   => PHP_VERSION,
            'memory_usage'  => round(memory_get_usage() / 1024 / 1024, 2) . ' MB',
            'memory_peak'   => round(memory_get_peak_usage() / 1024 / 1024, 2) . ' MB',
            'user_info' => [
                'ip'           => Common::getClientIp(),
                'os'           => Common::getOperatingSystem(),
                'browser'      => Common::getBrowser(),
                'request_uri'  => $_SERVER['REQUEST_URI'] ?? '/examples/check_email.php',
            ],
            'base_url'     => rtrim($_ENV['APP_URL'] ?? 'http://pay.jiamisoft.com/', '/') . '/',
            'send_time'    => date('Y-m-d H:i:s'),
        ];
        $body = $twig->render('system_error_alert.twig', $vars);
        $mailer->send($to, '【测试】系统错误报警', $body, [], true);
        json_response(['success' => true, 'message' => '系统错误报警邮件已发送至 ' . $to]);
    } catch (Throwable $e) {
        json_response(['success' => false, 'message' => '发送失败：' . $e->getMessage()], 500);
    }
}

// GET：渲染页面
?><!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>邮件发送测试</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; background:#f5f5f7; margin:0; padding:40px; }
        .card { max-width:720px; margin:0 auto; background:#fff; border:1px solid #e5e5e7; border-radius:12px; padding:24px; box-shadow:0 4px 20px rgba(0,0,0,.06); }
        h1 { margin:0 0 8px; font-size:22px; }
        p.desc { margin:0 0 16px; color:#6e6e73; }
        .actions { margin-top:12px; }
        .btn { display:inline-block; margin-right:12px; padding:10px 18px; background:#007aff; color:#fff; border-radius:8px; border:none; cursor:pointer; font-size:14px; }
        .btn.secondary{ background:#6c757d; }
        .btn:disabled{ opacity:.6; cursor:not-allowed; }
        #msg { margin-top:16px; font-size:14px; }
        .ok { color:#16a34a; }
        .err{ color:#dc2626; }
        .kv { color:#6e6e73; font-size:13px; margin-top:10px; }
        code { background:#f1f5f9; padding:2px 6px; border-radius:4px; }
    </style>
</head>
<body>
    <div class="card">
        <h1>邮件发送测试</h1>
        <p class="desc">点击按钮将异步发送测试邮件到 <code><?php echo htmlspecialchars($_ENV['ADM_EMAIL'] ?? '未配置'); ?></code></p>
        <div class="actions">
            <button id="btn-activation" class="btn">发送授权码测试邮件</button>
            <button id="btn-error" class="btn secondary">发送系统错误报警</button>
        </div>
        <div id="msg"></div>
        <div class="kv">要求环境变量：<code>ADM_EMAIL</code>、<code>MAIL_HOST</code>、<code>MAIL_PORT</code>、<code>MAIL_USERNAME</code>、<code>MAIL_PASSWORD</code>（以及可选 <code>MAIL_FROM_ADDRESS</code>、<code>MAIL_FROM_NAME</code>）</div>
    </div>

<script>
(function(){
    const msg = document.getElementById('msg');
    const btnAct = document.getElementById('btn-activation');
    const btnErr = document.getElementById('btn-error');

    function setLoading(loading){ btnAct.disabled = btnErr.disabled = loading; }

    function send(type){
        setLoading(true);
        msg.textContent = '发送中…'; msg.className='';
        fetch(location.href, {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: 'type=' + encodeURIComponent(type)
        })
        .then(r => r.json())
        .then(data => {
            msg.textContent = data.message || (data.success ? '发送完成' : '发送失败');
            msg.className = data.success ? 'ok' : 'err';
        })
        .catch(err => { msg.textContent = '请求失败：' + err; msg.className='err'; })
        .finally(() => setLoading(false));
    }

    btnAct.addEventListener('click', () => send('activation'));
    btnErr.addEventListener('click', () => send('error'));
})();
</script>
</body>
</html>
