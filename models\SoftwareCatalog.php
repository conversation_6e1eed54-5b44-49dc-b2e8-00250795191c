<?php
declare(strict_types=1);
namespace Models;
/**
 * 软件目录映射（PHP 版）
 * 对应 C++ SoftwareCatalog，提供名称 ? ID 互查。
 * 本文件编码 UTF-8 BOM。
 */
final class SoftwareCatalog
{
    /** @var array<string,int> 名称 → ID */
    private const NAME_TO_ID = [
        '文件夹加密超级大师'           => 1,
        '超级加密3000'               => 2,
        '文件夹只读加密专家'         => 3,
        '共享文件夹加密超级大师'     => 4,
        '文件夹保护3000'             => 5,
        '超级秘密文件夹'             => 6,
        'U盘超级加密3000'            => 7,
        'U盘内存卡批量只读加密专家' => 8,
        'CHK文件恢复专家'           => 9,
        '定时关机3000'               => 10,
        '超级秘密磁盘3000'           => 11,
        '磁盘加锁专家'               => 12,
        '金钻视频加密专家'           => 13,
        '电脑监控专家'               => 14,
        'Best Folder Encryptor'      => 15,
        'Best Encryption Expert'     => 16,
        'Easy Folder Guard'          => 17,
        'Ace Secret Folder'          => 18,
        'USB Encryptor'              => 19,
        'PC Monitor Expert'          => 20,
        'Magic Timed Shutdown'       => 21,
        'CHK File Recovery'          => 22,
        'Best Disk Lock'             => 23,
        'Ace Secret Disk'            => 24,
        'VideoGuard Pro'             => 25,
        'FolderLock ReadOnly'        => 26,
        'SharedFolder CipherLock Pro'=> 27,
        'USB Batch Encryptor Pro'    => 28,
        // 别名：同一软件的另一名称
        'USB/SD Batch Encryptor Pro' => 28,
    ];

    private const ID_TO_ID = [
        'BEF0001'           => 1,
        'BSE0004'               => 2,
        'FRE0015'         => 3,
        'SFE0012'     => 4,
        'EFG0008'             => 5,
        'ASF0006'             => 6,
        'UE00002'            => 7,
        'MCE0012' => 8,
        'CFR0009'           => 9,
        'MTS0003'               => 10,
        'ASD0010'           => 11,
        'BDL0007'               => 12,
        'VE00013'           => 13,
        'PME0005'               => 14,
        'BEF1001'      => 15,
        'BSE1004'     => 16,
        'EFG1008'          => 17,
        'ASF1006'          => 18,
        'UE10002'              => 19,
        'PME1005'          => 20,
        'MTS1003'       => 21,
        'CFR1009'          => 22,
        'BDL1007'             => 23,
        'ASD1010'            => 24,
        'VE10013'             => 25,
        'FRE1015'        => 26,
        'SFE1012'=> 27,
        'MCE1012'    => 28
    ];

    /** @var array<int,string> 缓存 ID → 名称（索引 0 预留空字符串），首次使用时生成 */
    private static array $ID_TO_NAME = [];

    /** @var array<int,string> 缓存 ID → 代码（索引 0 预留空字符串），首次使用时生成 */
    private static array $ID_TO_CODE = [];

    /**
     * 构建 ID → 名称映射表（懒加载）
     */
    private static function buildIdTable(): void
    {
        if (self::$ID_TO_NAME === []) {
            self::$ID_TO_NAME = array_flip(self::NAME_TO_ID);
            self::$ID_TO_NAME[0] = '';
        }
    }

    /**
     * 构建 ID → 代码映射表（懒加载）
     */
    private static function buildCodeTable(): void
    {
        if (self::$ID_TO_CODE === []) {
            self::$ID_TO_CODE = array_flip(self::ID_TO_ID);
            self::$ID_TO_CODE[0] = '';
        }
    }

    /**
     * 由软件名称获取 ID。
     * @param string $name 输入软件名称
     * @param int    $id   返回的软件 ID（引用传出）
     * @return bool  成功返回 true，失败 false
     */
    public static function nameToId(string $name, int &$id): bool
    {
        if (isset(self::NAME_TO_ID[$name])) {
            $id = self::NAME_TO_ID[$name];
            return true;
        }
        return false;
    }

    /**
     * 由 ID 获取软件名称。
     * @param int    $id   软件 ID (0-127)
     * @param string $name 返回的软件名称（引用传出）
     * @return bool  成功返回 true，失败 false
     */
    public static function idToName(int $id, string &$name): bool
    {
        if ($id <= 0 || $id > 127) {
            return false;
        }
        self::buildIdTable();
        if (!isset(self::$ID_TO_NAME[$id]) || self::$ID_TO_NAME[$id] === '') {
            return false;
        }
        $name = self::$ID_TO_NAME[$id];
        return true;
    }

    /**
     * 由软件代码获取 ID。
     * @param string $code 输入软件代码
     * @param int    $id   返回的软件 ID（引用传出）
     * @return bool  成功返回 true，失败 false
     */
    public static function codeToId(string $code, int &$id): bool
    {
        if (isset(self::ID_TO_ID[$code])) {
            $id = self::ID_TO_ID[$code];
            return true;
        }
        return false;
    }

    /**
     * 由 ID 获取软件代码。
     * @param int    $id   软件 ID (0-127)
     * @param string $code 返回的软件代码（引用传出）
     * @return bool  成功返回 true，失败 false
     */
    public static function idToCode(int $id, string &$code): bool
    {
        if ($id <= 0 || $id > 127) {
            return false;
        }
        self::buildCodeTable();
        if (!isset(self::$ID_TO_CODE[$id]) || self::$ID_TO_CODE[$id] === '') {
            return false;
        }
        $code = self::$ID_TO_CODE[$id];
        return true;
    }
}
?> 