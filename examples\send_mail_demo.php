<?php
// send_mail_demo.php - 示例脚本：发送授权码与系统错误报警邮件
// 运行方式：在终端执行 `php examples/send_mail_demo.php`
// 依赖：确保已配置 MAIL_* 环境变量，或在 .env 中设置对应值。

declare(strict_types=1);
require_once __DIR__ . '/../boot.php';

use App\Lib\Mailer;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;

// -------------------------------------------------------------
// 初始化日志（将 PHPMailer 调试信息输出到终端）
// -------------------------------------------------------------
$logger = new Logger('mail_demo');
$logger->pushHandler(new StreamHandler('php://stdout', Logger::DEBUG));

// -------------------------------------------------------------
// 初始化 Mailer 封装
// -------------------------------------------------------------
$mailer = new Mailer($logger);

// -------------------------------------------------------------
// 增强的模板渲染函数
// -------------------------------------------------------------
/**
 * @param string $template 原始模板内容
 * @param array  $vars     变量映射，如 ['company_name' => 'jiamisoft']
 */
function render(string $template, array $vars): string
{
    // 1. 处理嵌套对象属性 (如 user_info.ip)
    $template = preg_replace_callback('/\{\{\s*(\w+)\.(\w+)([^}]*)\}\}/', function($matches) use ($vars) {
        $objName = $matches[1];
        $propName = $matches[2];
        $rest = $matches[3]; // 可能包含过滤器
        
        // 查找嵌套值
        $value = '';
        if (isset($vars[$objName]) && is_array($vars[$objName]) && isset($vars[$objName][$propName])) {
            $value = $vars[$objName][$propName];
        } elseif (isset($vars[$objName . '_' . $propName])) {
            $value = $vars[$objName . '_' . $propName];
        }
        
        // 处理默认值
        if (preg_match('/\|default\([\'"]([^\'"]*)[\'"]?\)/', $rest, $defaultMatch)) {
            if (empty($value)) {
                $value = $defaultMatch[1];
            }
        }
        
        // 处理字符串截取
        if (preg_match('/\|slice\((\d+),\s*(\d+)\)/', $rest, $sliceMatch)) {
            $start = (int)$sliceMatch[1];
            $length = (int)$sliceMatch[2];
            $value = substr($value, $start, $length);
        }
        
        return $value;
    });
    
    // 2. 处理简单变量的默认值过滤器
    $template = preg_replace_callback('/\{\{\s*(\w+)\|default\([\'"]([^\'"]*)[\'"]?\)\s*\}\}/', function($matches) use ($vars) {
        $varName = $matches[1];
        $defaultValue = $matches[2];
        return $vars[$varName] ?? $defaultValue;
    }, $template);
    
    // 3. 处理日期过滤器
    $template = preg_replace_callback('/\{\{\s*[\'"]now[\'"]?\|date\([\'"]([^\'"]*)[\'"]?\)\s*\}\}/', function($matches) {
        $format = $matches[1];
        return date($format);
    }, $template);
    
    // 4. 处理简单变量替换
    foreach ($vars as $key => $value) {
        if (is_scalar($value) || is_null($value)) {
            $template = str_replace(['{{ ' . $key . ' }}', '{{' . $key . '}}'], (string) $value, $template);
        }
    }
    
    // 5. 处理条件语句 {% if variable %}...{% endif %}
    $template = preg_replace_callback('/\{%\s*if\s+(\w+)\s*%\}(.*?)\{%\s*endif\s*%\}/s', function($matches) use ($vars) {
        $varName = $matches[1];
        $content = $matches[2];
        return (!empty($vars[$varName])) ? $content : '';
    }, $template);
    
    // 6. 处理循环语句 {% for key, value in array %}...{% endfor %}
    $template = preg_replace_callback('/\{%\s*for\s+(\w+),\s*(\w+)\s+in\s+(\w+)\s*%\}(.*?)\{%\s*endfor\s*%\}/s', function($matches) use ($vars) {
        $keyVar = $matches[1];
        $valueVar = $matches[2];
        $arrayVar = $matches[3];
        $content = $matches[4];
        
        if (!isset($vars[$arrayVar]) || !is_array($vars[$arrayVar])) {
            return '';
        }
        
        $result = '';
        foreach ($vars[$arrayVar] as $k => $v) {
            $loopContent = str_replace(
                ['{{ ' . $keyVar . ' }}', '{{ ' . $valueVar . ' }}'],
                [$k, $v],
                $content
            );
            $result .= $loopContent;
        }
        
        return $result;
    }, $template);
    
    // 7. 清理剩余的未处理模板语法
    $template = preg_replace('/\{%[^%]*%\}/', '', $template);
    $template = preg_replace('/\{\{[^}]*\}\}/', '', $template);
    
    return $template;
}

// 检查邮件配置
$toAddress = $_ENV['ADM_EMAIL'] ?? ($_ENV['MAIL_USERNAME'] ?? '<EMAIL>');

$requiredMailEnvs = ['MAIL_HOST', 'MAIL_USERNAME', 'MAIL_PASSWORD'];
$missingEnvs = [];
foreach ($requiredMailEnvs as $env) {
    if (empty($_ENV[$env])) {
        $missingEnvs[] = $env;
    }
}

if (!empty($missingEnvs)) {
    echo "错误：缺少必要的邮件配置环境变量：" . implode(', ', $missingEnvs) . "\n";
    echo "请在 .env 文件中配置这些变量\n";
    exit(1);
}

// -------------------------------------------------------------
// 1) 发送授权码邮件
// -------------------------------------------------------------
$activationTplPath = __DIR__ . '/../templates/email/activation_code.twig';
if (!file_exists($activationTplPath)) {
    echo "错误：激活码邮件模板文件不存在：{$activationTplPath}\n";
    exit(1);
}
$activationTpl = file_get_contents($activationTplPath);

$activationVars = [
    'company_name'   => '洛阳夏冰软件技术有限公司',
    'software_name'  => '测试软件',
    'activation_code'=> 'ABCD-EFGH-IJKL-MNOP',
    'download_url'   => 'https://example.com/download',
    'service_phone'  => '************',
    'service_email'  => '<EMAIL>',
    'work_time'      => '工作日 9:00 - 12:00  14:00 - 17:30',
    'website_url'    => 'https://www.jiamisoft.com/',
    'qr_code_url'    => '',
    'order_number'   => 'TEST123456',
    'send_time'      => date('Y-m-d H:i:s'),
];

$activationBody = render($activationTpl, $activationVars);

try {
    $mailer->send($toAddress, '【测试】软件授权码', $activationBody, [], true);
    echo "授权码测试邮件已发送至 {$toAddress}\n";
} catch (Throwable $e) {
    echo "发送授权码邮件失败：" . $e->getMessage() . "\n";
}

// -------------------------------------------------------------
// 2) 发送系统错误报警邮件
// -------------------------------------------------------------
$errorTplPath = __DIR__ . '/../templates/email/system_error_alert.twig';
if (!file_exists($errorTplPath)) {
    echo "错误：系统错误报警邮件模板文件不存在：{$errorTplPath}\n";
    exit(1);
}
$errorTpl = file_get_contents($errorTplPath);

$errorVars = [
    'error_title'   => '数据库连接异常',
    'error_message' => 'PDOException: Access denied for user',
    'error_file'    => __FILE__,
    'error_line'    => 0,
    'error_time'    => date('Y-m-d H:i:s'),
    'error_level'   => 'CRITICAL',
    'error_color'   => '#dc3545',
    'company_name'  => '洛阳夏冰软件技术有限公司',
    'server_info'   => php_uname(),
    'php_version'   => PHP_VERSION,
    'memory_usage'  => round(memory_get_usage()/1024/1024, 2) . ' MB',
    'memory_peak'   => round(memory_get_peak_usage()/1024/1024, 2) . ' MB',
    'base_url'      => 'http://pay.jiamisoft.com/',
    'service_email' => '<EMAIL>',
    'service_phone' => '************',
    'send_time'     => date('Y-m-d H:i:s'),
    'user_info'     => [
        'ip' => '127.0.0.1',
        'os' => PHP_OS,
        'browser' => 'CLI',
        'request_uri' => '/examples/send_mail_demo.php',
    ],
    'system_status' => [
        '磁盘使用率' => '45%',
        'CPU使用率' => '12%',
        '数据库连接' => '正常',
    ],
    'error_trace' => "Stack trace:\n#0 /path/to/file.php(123): function_name()\n#1 /path/to/another.php(456): another_function()",
    'error_id' => 'ERR_' . date('YmdHis') . '_' . rand(1000, 9999),
];

$errorBody = render($errorTpl, $errorVars);

try {
    $mailer->send($toAddress, '【测试】系统错误报警', $errorBody, [], true);
    echo "系统错误报警邮件已发送至 {$toAddress}\n";
} catch (Throwable $e) {
    echo "发送系统错误报警邮件失败：" . $e->getMessage() . "\n";
}

echo "\n邮件发送测试完成！\n";
