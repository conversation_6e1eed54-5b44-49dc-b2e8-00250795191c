<?php
// send_mail_demo.php - 示例脚本：发送授权码与系统错误报警邮件
// 运行方式：在终端执行 `php examples/send_mail_demo.php`
// 依赖：确保已配置 MAIL_* 环境变量，或在 .env 中设置对应值。

declare(strict_types=1);

use App\Lib\Mailer;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;

require_once __DIR__ . '/../vendor/autoload.php';

// -------------------------------------------------------------
// 初始化日志（将 PHPMailer 调试信息输出到终端）
// -------------------------------------------------------------
$logger = new Logger('mail_demo');
$logger->pushHandler(new StreamHandler('php://stdout', Logger::DEBUG));

// -------------------------------------------------------------
// 初始化 Mailer 封装
// -------------------------------------------------------------
$mailer = new Mailer($logger);

// -------------------------------------------------------------
// 简易模板渲染函数（仅做占位符替换，生产环境推荐使用 Twig 引擎）
// -------------------------------------------------------------
/**
 * @param string $template 原始模板内容
 * @param array  $vars     变量映射，如 ['company_name' => 'jiamisoft']
 */
function render(string $template, array $vars): string
{
    foreach ($vars as $key => $value) {
        // 兼容 {{ key }} / {{key}} 两种写法
        $template = str_replace(['{{ ' . $key . ' }}', '{{' . $key . '}}'], (string) $value, $template);
    }
    return $template;
}

// -------------------------------------------------------------
// 1) 发送授权码邮件
// -------------------------------------------------------------
$activationTplPath = __DIR__ . '/../templates/email/activation_code.twig';
$activationTpl     = file_exists($activationTplPath) ? file_get_contents($activationTplPath) : '';

$activationVars = [
    'company_name' => '洛阳夏冰软件技术有限公司',
    'license_code' => 'ABCD-EFGH-IJKL-MNOP',
];

$activationBody = render($activationTpl, $activationVars);

$toAddress = $_ENV['TEST_MAIL_TO'] ?? ($_ENV['MAIL_USERNAME'] ?? '<EMAIL>');

try {
    $mailer->send($toAddress, '【测试】软件授权码', $activationBody, [], true);
    echo "授权码测试邮件已发送至 {$toAddress}\n";
} catch (Throwable $e) {
    echo "发送授权码邮件失败：" . $e->getMessage() . "\n";
}

// -------------------------------------------------------------
// 2) 发送系统错误报警邮件
// -------------------------------------------------------------
$errorTplPath = __DIR__ . '/../templates/email/system_error_alert.twig';
$errorTpl     = file_exists($errorTplPath) ? file_get_contents($errorTplPath) : '';

$errorVars = [
    'error_title'   => '数据库连接异常',
    'error_message' => 'PDOException: Access denied for user',
    'error_file'    => __FILE__,
    'error_line'    => 0,
    'error_time'    => date('Y-m-d H:i:s'),
    'error_level'   => 'CRITICAL',
    'company_name'  => '洛阳夏冰软件技术有限公司',
];

$errorBody = render($errorTpl, $errorVars);

try {
    $mailer->send($toAddress, '【测试】系统错误报警', $errorBody, [], true);
    echo "系统错误报警邮件已发送至 {$toAddress}\n";
} catch (Throwable $e) {
    echo "发送系统错误报警邮件失败：" . $e->getMessage() . "\n";
}
