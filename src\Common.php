<?php
namespace App\Lib;
/**
 * 通用工具类
 *
 * 包含IP获取等通用方法。
 */
class Common
{
    /**
     * 获取客户端IP地址（静态方法）
     *
     * 此函数优先从REMOTE_ADDR获取IP，然后检查常见代理头。
     * 支持IPv4/IPv6验证，并添加基本反伪造检查。
     * 注意：生产环境中，应结合trusted proxies列表进一步验证。
     *
     * @return string 有效IP地址，如果无法获取返回'0.0.0.0'
     */
    public static function getClientIp(): string
    {
        $ip = '0.0.0.0';

        // 1. 标准客户端IP
        if (!empty($_SERVER['REMOTE_ADDR']) && filter_var($_SERVER['REMOTE_ADDR'], FILTER_VALIDATE_IP)) {
            $ip = $_SERVER['REMOTE_ADDR'];
        }

        // 2. 处理反向代理
        $proxyHeaders = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
        ];

        foreach ($proxyHeaders as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                foreach ($ips as $candidate) {
                    $candidate = trim($candidate);
                    if (filter_var($candidate, FILTER_VALIDATE_IP)
                        && !filter_var($candidate, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)
                    ) {
                        return $candidate;
                    }
                }
            }
        }

        return $ip;
    }

    /**
     * 获取客户端操作系统信息
     *
     * @return string 操作系统名称，未知则返回 'Unknown'
     */
    public static function getOperatingSystem(): string
    {
        // 优先使用 User-Agent Client Hints 检测 Windows 11
        // 资料来源: https://learn.microsoft.com/en-us/microsoft-edge/web-platform/how-to-detect-win11
        if (isset($_SERVER['HTTP_SEC_CH_UA_PLATFORM_VERSION'])) {
            $platformVersion = trim($_SERVER['HTTP_SEC_CH_UA_PLATFORM_VERSION'], '"');
            // Windows 11 的版本号通常是 "13.0.0" 或更高, Windows 10 是 "10.0.0"
            if (version_compare($platformVersion, '13.0.0', '>=')) {
                return 'Windows 11';
            }
        }

        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        $osPlatform = 'Unknown';

        // 操作系统识别规则，整合了新旧逻辑
        $osArray = [
            // Windows (Win11的User-Agent可能与Win10相同，作为后备)
            '/windows nt 11.0/i'    =>  'Windows 11',
            '/windows nt 10.0/i'    =>  'Windows 10',
            '/windows nt 6.3/i'     =>  'Windows 8.1',
            '/windows nt 6.2/i'     =>  'Windows 8',
            '/windows nt 6.1/i'     =>  'Windows 7',
            '/windows nt 6.0/i'     =>  'Windows Vista',
            '/windows nt 5.2/i'     =>  'Windows Server 2003/XP x64',
            '/windows nt 5.1/i'     =>  'Windows XP',
            '/windows xp/i'         =>  'Windows XP',
            '/windows nt 5.0/i'     =>  'Windows 2000',
            '/windows me/i'         =>  'Windows ME',
            '/win98/i'              =>  'Windows 98',
            '/win95/i'              =>  'Windows 95',
            '/winnt/i'              =>  'Windows NT',
            // Mac
            '/macintosh|mac os x/i' =>  'Mac OS X',
            '/mac_powerpc/i'        =>  'Mac OS 9',
            // Linux
            '/linux/i'              =>  'Linux',
            '/ubuntu/i'             =>  'Ubuntu',
            // 移动设备
            '/iphone/i'             =>  'iPhone',
            '/ipod/i'               =>  'iPod',
            '/ipad/i'               =>  'iPad',
            '/android/i'            =>  'Android',
            '/blackberry/i'         =>  'BlackBerry',
            '/webos/i'              =>  'Mobile',
            // 其他 *nix
            '/sunos/i'              =>  'SunOS',
            '/beos/i'               =>  'BeOS',
            '/freebsd/i'            =>  'FreeBSD',
            '/openbsd/i'            =>  'OpenBSD',
            '/netbsd/i'             =>  'NetBSD',
            '/osf1/i'               =>  'OSF1',
            '/irix/i'               =>  'IRIX',
            '/hp-ux/i'              =>  'HP-UX',
            '/aix/i'                =>  'AIX',
            '/unix/i'               =>  'Unix',
        ];

        foreach ($osArray as $regex => $value) {
            if (preg_match($regex, $userAgent)) {
                $osPlatform = $value;
                break; // 找到后即跳出循环
            }
        }

        // 检查是否为64位系统
        if (preg_match('/(wow64|win64|x64)/i', $userAgent)) {
            if ($osPlatform !== 'Unknown' && strpos($osPlatform, '64bit') === false) {
                $osPlatform .= ' 64bit';
            }
        }

        return $osPlatform;
    }

    /**
     * 判断是否为移动端
     *
     * @return bool
     */
    public static function isMobile(): bool
    {
        // 1. 检查 HTTP_X_WAP_PROFILE：最可靠的标志
        if (isset($_SERVER['HTTP_X_WAP_PROFILE'])) {
            return true;
        }

        // 2. 检查 HTTP_VIA 是否包含 'wap'：部分服务商会提供
        if (isset($_SERVER['HTTP_VIA'])) {
            if (stristr($_SERVER['HTTP_VIA'], 'wap') !== false) {
                return true;
            }
        }

        // 3. 检查 User-Agent：最常用但兼容性需要持续维护
        if (isset($_SERVER['HTTP_USER_AGENT'])) {
            $clientKeywords = [
                'nokia', 'sony', 'ericsson', 'mot', 'samsung', 'htc', 'sgh', 'lg', 'sharp',
                'sie-', 'philips', 'panasonic', 'alcatel', 'lenovo', 'iphone', 'ipod',
                'blackberry', 'meizu', 'android', 'netfront', 'symbian', 'ucweb', 'windowsce',
                'palm', 'operamini', 'operamobi', 'openwave', 'nexusone', 'cldc', 'midp',
                'wap', 'mobile'
                // 'MicroMessenger' 已移除，因为它可能代表桌面版微信
            ];
            $userAgent = strtolower($_SERVER['HTTP_USER_AGENT']);
            if (preg_match('/(' . implode('|', $clientKeywords) . ')/i', $userAgent)) {
                return true;
            }
        }

        // 4. 检查 HTTP_ACCEPT 头：作为最后的备用方案
        if (isset($_SERVER['HTTP_ACCEPT'])) {
            $accept = $_SERVER['HTTP_ACCEPT'];
            // 如果只支持wml并且不支持html那一定是移动设备
            // 如果支持wml和html但是wml在html之前则是移动设备
            if ((strpos($accept, 'vnd.wap.wml') !== false) &&
                (strpos($accept, 'text/html') === false || (strpos($accept, 'vnd.wap.wml') < strpos($accept, 'text/html'))))
            {
                return true;
            }
        }

        return false;
    }

    public static function getBrowser(): string
    {
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        $browser = 'Unknown';

        $browserArray = [
            // 主流浏览器
            '/msie/i'       =>  'Internet Explorer',
            '/firefox/i'    =>  'Firefox',
            '/safari/i'     =>  'Safari',
            '/chrome/i'     =>  'Chrome',
            '/edge/i'       =>  'Edge',
            '/opera/i'      =>  'Opera',
            '/netscape/i'   =>  'Netscape',
            '/maxthon/i'    =>  'Maxthon',
            '/konqueror/i'  =>  'Konqueror',
            // 移动端或内嵌浏览器
            '/micromessenger/i' => 'WeChat',
            '/alipay/i'         => 'Alipay',
            '/ucweb/i'          => 'UC Browser',
            '/qqbrowser/i'      => 'QQ Browser',
            '/baidubrowser/i'   => 'Baidu Browser',
            '/liebaofast/i'     => 'Cheetah Mobile',
        ];

        foreach ($browserArray as $regex => $value) {
            if (preg_match($regex, $userAgent)) {
                $browser = $value;
                break;
            }
        }

        // 特殊处理，Chrome和Safari经常同时出现
        if (preg_match('/chrome/i', $userAgent) && preg_match('/safari/i', $userAgent)) {
            $browser = 'Chrome';
        }

        return $browser;
    }
}