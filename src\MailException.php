<?php

declare(strict_types=1);

namespace App\Lib;

use RuntimeException;

/**
 * 自定义邮件异常。
 *
 * 用于封装 PHPMailer 的错误，方便调用层统一捕获。
 */
class MailException extends RuntimeException
{
    /**
     * SMTP 服务器返回的原始错误信息。
     */
    private string $mailerErrorInfo;

    public function __construct(
        string $message = '',
        int $code = 0,
        ?\Throwable $previous = null,
        string $mailerErrorInfo = ''
    ) {
        parent::__construct($message, $code, $previous);
        $this->mailerErrorInfo = $mailerErrorInfo;
    }

    /**
     * 获取 SMTP 服务器返回的 ErrorInfo。
     */
    public function getMailerErrorInfo(): string
    {
        return $this->mailerErrorInfo;
    }
}
