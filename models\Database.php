<?php
namespace Models;

require_once '/www/wwwroot/config/db/soft-shop.php';
/**
 * 数据库连接类
 * - 使用.env文件进行配置驱动
 * - 支持通过DSN字符串或独立参数进行配置
 * - 采用单例模式确保全局只有一个PDO实例
 * - 使用PDO进行数据库操作，保证安全性和现代性
 */
class Database
{
    private static array $instances = [];

    private function __construct() {}
    private function __clone() {}

    public function __wakeup()
    {
        throw new \Exception("Cannot unserialize a multiton.");
    }

    /**
     * 获取PDO实例
     *
     * @param string $key 配置键 (e.g., 'default', 'analytics')
     * @return \PDO
     * @throws \Exception
     */
    public static function getInstance(string $key = 'default'): \PDO
    {
        if (!isset(self::$instances[$key])) {
            try {
                $dsn = self::buildDsn($key);
                $user = self::getDbUser($key);
                $pass = self::getDbPassword($key);

                if (!$dsn || !$user) {
                    throw new \Exception("数据库配置 '$key' 不完整，请检查.env文件。");
                }

                self::$instances[$key] = new \PDO($dsn, $user, $pass, [
                    \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                    \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC,
                    \PDO::ATTR_EMULATE_PREPARES => false,
                ]);
            } catch (\PDOException $e) {
                if (isset($GLOBALS['log'])) {
                    $GLOBALS['log']->error("数据库 '$key' 连接失败: " . $e->getMessage());
                }
                throw new \Exception("数据库 '$key' 连接失败，请检查配置。 ");
            }
        }

        return self::$instances[$key];
    }

    private static function getEnvVar(string $key, string $suffix): ?string
    {
        $prefix = ($key === 'default') ? '' : strtoupper($key) . '_';
        return $_ENV[$prefix . $suffix] ?? null;
    }

    private static function getDbUser(string $key): ?string
    {
        return PC_DB_USER;
    }

    private static function getDbPassword(string $key): ?string
    {
        return PC_DB_PASSWORD;
    }

    private static function buildDsn(string $key): ?string
    {
        $dsn = self::getEnvVar($key, 'DB_DSN');
        if ($dsn) {
            return $dsn;
        }

        $host = PC_DB_HOST;
        $name = PC_DB_NAME;
        $charset = PC_DB_CHARSET;

        if ($host && $name) {
            return "mysql:host={$host};dbname={$name};charset={$charset}";
        }

        return null;
    }
}