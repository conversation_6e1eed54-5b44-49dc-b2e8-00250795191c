<?php
// send_mail_api.php - AJAX 邮件发送接口
// POST 参数: type = activation | error
// 返回 JSON: { success: bool, message: string }

declare(strict_types=1);

header('Content-Type: application/json; charset=utf-8');

// 仅允许 POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method Not Allowed']);
    exit;
}

require_once __DIR__ . '/../boot.php';

use App\Lib\Mailer;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;

$type = $_POST['type'] ?? '';
if (!in_array($type, ['activation', 'error'], true)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid type']);
    exit;
}

$logger = new Logger('mail_api');
$logger->pushHandler(new StreamHandler('php://stderr', Logger::INFO));
$mailer = new Mailer($logger);

$toAddress = $_ENV['ADM_EMAIL'] ?? ($_ENV['MAIL_USERNAME'] ?? '');
if (empty($toAddress)) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '收件人邮箱未配置 (ADM_EMAIL)']);
    exit;
}

/**
 * 简易变量替换渲染 (仅 {{ key }} 占位符)。
 */
function render(string $tpl, array $vars): string
{
    foreach ($vars as $k => $v) {
        $tpl = str_replace(['{{ ' . $k . ' }}', '{{' . $k . '}}'], (string) $v, $tpl);
    }
    return $tpl;
}

try {
    if ($type === 'activation') {
        $tpl = file_get_contents(__DIR__ . '/../templates/email/activation_code.twig');
        $vars = [
            'company_name'    => '洛阳夏冰软件技术有限公司',
            'software_name'   => '测试软件',
            'activation_code' => 'ABCD-EFGH-IJKL-MNOP',
            'download_url'    => 'https://example.com/download',
            'service_phone'   => '************',
            'service_email'   => '<EMAIL>',
            'work_time'       => '工作日 9:00 - 12:00  14:00 - 17:30',
            'website_url'     => 'https://www.jiamisoft.com/',
            'order_number'    => 'TEST123456',
            'send_time'       => date('Y-m-d H:i:s'),
        ];
        $subject = '【测试】软件授权码';
        $body    = render($tpl, $vars);
    } else { // error
        $tpl = file_get_contents(__DIR__ . '/../templates/email/system_error_alert.twig');
        $vars = [
            'error_title'   => '数据库连接异常',
            'error_message' => 'PDOException: Access denied for user',
            'error_file'    => __FILE__,
            'error_line'    => 0,
            'error_time'    => date('Y-m-d H:i:s'),
            'error_level'   => 'CRITICAL',
            'error_color'   => '#dc3545',
            'company_name'  => '洛阳夏冰软件技术有限公司',
            'server_info'   => php_uname(),
            'php_version'   => PHP_VERSION,
            'memory_usage'  => round(memory_get_usage() / 1024 / 1024, 2) . ' MB',
            'memory_peak'   => round(memory_get_peak_usage() / 1024 / 1024, 2) . ' MB',
            'user_info.ip'          => '127.0.0.1',
            'user_info.os'          => PHP_OS,
            'user_info.browser'     => 'CLI',
            'user_info.request_uri' => '/examples/send_mail_api.php',
            'base_url'     => 'http://pay.jiamisoft.com/',
            'send_time'    => date('Y-m-d H:i:s'),
        ];
        $subject = '【测试】系统错误报警';
        $body    = render($tpl, $vars);
    }

    $mailer->send($toAddress, $subject, $body, [], true);

    echo json_encode(['success' => true, 'message' => '邮件已发送至 ' . $toAddress]);
} catch (Throwable $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '发送失败：' . $e->getMessage()]);
}
