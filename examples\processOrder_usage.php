<?php
/**
 * PayTools::processOrder 方法使用示例
 * 
 * 展示如何在支付回调中使用改进后的订单处理方法
 */

require_once '../boot.php';

use App\Lib\PayTools;
use Monolog\Logger;

// 模拟支付宝回调参数（实际使用中这些参数来自 $_GET 或 $_POST）
$mockAlipayCallback = [
    'out_trade_no' => 'L731675318089303',
    'trade_no' => '2025073122001453011402440441',
    'total_amount' => '1.00',
    'timestamp' => '2025-01-01 12:00:00',
    'app_id' => '2021002102688488',
    'seller_id' => '2088901265902011'
];

// 创建日志记录器
$logger = new Logger('order_processing');
$logPath = $_ENV['LOG_PATH'] . '/examples/';
if (!is_dir($logPath)) {
    mkdir($logPath, 0750, true);
}
$logger->pushHandler(new \Monolog\Handler\StreamHandler($logPath . 'process_order.log', Logger::INFO));

echo "<h2>PayTools::processOrder 使用示例</h2>";

try {
    // ============================================
    // 示例1：正常订单处理流程
    // ============================================
    echo "<h3>1. 正常订单处理</h3>";
    
    $out_trade_no = $mockAlipayCallback['out_trade_no'];
    
    // 使用改进后的 processOrder 方法
    $result = PayTools::processOrder($out_trade_no, $mockAlipayCallback, $logger);
    
    if ($result['success']) {
        echo "<p>✅ 订单处理成功！</p>";
        echo "<ul>";
        echo "<li>订单号: " . htmlspecialchars($result['out_trade_no']) . "</li>";
        echo "<li>支付宝交易号: " . htmlspecialchars($result['trade_no']) . "</li>";
        echo "<li>授权码: " . htmlspecialchars($result['soft_key']) . "</li>";
        echo "<li>更新字段: " . json_encode($result['updated_fields']) . "</li>";
        echo "</ul>";
    }
    
} catch (\InvalidArgumentException $e) {
    echo "<p>❌ 参数验证失败: " . htmlspecialchars($e->getMessage()) . "</p>";
} catch (\Exception $e) {
    echo "<p>❌ 订单处理失败: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// ============================================
// 示例2：参数验证测试
// ============================================
echo "<h3>2. 参数验证测试</h3>";

$testCases = [
    [
        'name' => '空订单号',
        'out_trade_no' => '',
        'params' => $mockAlipayCallback
    ],
    [
        'name' => '无效支付宝交易号',
        'out_trade_no' => 'TEST123',
        'params' => array_merge($mockAlipayCallback, ['trade_no' => 'invalid_trade_no'])
    ],
    [
        'name' => '负数金额',
        'out_trade_no' => 'TEST123',
        'params' => array_merge($mockAlipayCallback, ['total_amount' => '-1.00'])
    ],
    [
        'name' => '无效时间戳格式',
        'out_trade_no' => 'TEST123',
        'params' => array_merge($mockAlipayCallback, ['timestamp' => '2025/01/01 12:00:00'])
    ],
    [
        'name' => '未来时间戳',
        'out_trade_no' => 'TEST123',
        'params' => array_merge($mockAlipayCallback, ['timestamp' => date('Y-m-d H:i:s', time() + 3600)])
    ]
];

foreach ($testCases as $testCase) {
    echo "<h4>{$testCase['name']}</h4>";
    try {
        PayTools::processOrder($testCase['out_trade_no'], $testCase['params'], $logger);
        echo "<p>❌ 应该抛出异常但没有抛出</p>";
    } catch (\InvalidArgumentException $e) {
        echo "<p>✅ 正确捕获参数错误: " . htmlspecialchars($e->getMessage()) . "</p>";
    } catch (\Exception $e) {
        echo "<p>⚠️ 其他错误: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

// ============================================
// 示例3：订单号清理功能测试
// ============================================
echo "<h3>3. 订单号清理功能</h3>";

$dirtyOrderNos = [
    'ABC123-def_456',        // 正常订单号
    'ABC<script>alert(1)</script>123',  // 包含恶意脚本
    'ABC\'"; DROP TABLE orders; --',     // SQL注入尝试
    'ABC中文123',            // 包含中文字符
    'ABC@#$%^&*()123'       // 包含特殊字符
];

foreach ($dirtyOrderNos as $dirtyOrderNo) {
    try {
        $cleaned = PayTools::sanitizeOrderNo($dirtyOrderNo);
        echo "<p>原始: <code>" . htmlspecialchars($dirtyOrderNo) . "</code> → 清理后: <code>" . htmlspecialchars($cleaned) . "</code></p>";
    } catch (\InvalidArgumentException $e) {
        echo "<p>❌ 订单号无效: <code>" . htmlspecialchars($dirtyOrderNo) . "</code> - " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

echo "<hr>";
echo "<h3>方法改进总结</h3>";
echo "<ul>";
echo "<li>✅ 添加了完整的参数验证</li>";
echo "<li>✅ 改进了错误处理和异常类型</li>";
echo "<li>✅ 增强了日志记录</li>";
echo "<li>✅ 添加了安全性检查（订单号清理、时间戳验证等）</li>";
echo "<li>✅ 提供了辅助验证方法</li>";
echo "<li>✅ 保持了与原有代码的兼容性</li>";
echo "</ul>";

echo "<h3>在 aliReturn.php 中的使用方式</h3>";
echo "<pre><code>";
echo htmlspecialchars('
// 替换原来的 92-166 行处理逻辑
try {
    // 清理订单号（可选，增强安全性）
    $clean_order_no = PayTools::sanitizeOrderNo($out_trade_no);
    
    // 处理订单
    $result = PayTools::processOrder($clean_order_no, $filteredParams, $logger);
    
    // 使用返回的结果
    $soft_key = $result["soft_key"];
    $soft_info = $result["soft_info"];
    $order_info = $result["order_info"];
    
    // 继续原有的邮件发送等逻辑...
    
} catch (\InvalidArgumentException $e) {
    // 参数验证错误
    $logger->error("参数验证失败: " . $e->getMessage());
    // 返回错误页面或重定向
} catch (\Exception $e) {
    // 其他处理错误
    $logger->error("订单处理失败: " . $e->getMessage());
    // 返回错误页面或重定向
}
');
echo "</code></pre>";
?>