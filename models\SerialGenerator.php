<?php
declare(strict_types=1);
namespace Models;
use App\Lib\Base32; 

/**
 * 序列号生成器（22 字节：Payload 10B + MAC1 4B + MAC2 8B）
 * 本文件采用 UTF-8编码。
 */
class SerialGenerator
{
    /**
     * 生成 41 字符序列号（6-6-6-6-6-6）
     * @param int    $version     版本号 0-15
     * @param int    $swId        软件 ID 0-127
     * @param string $contact     用户联系方式 (原文)
     * @param int    $expireUnix  到期时间 (Unix 时间戳)
     */
    public static function generate(int $version, int $swId, string $contact, int $expireUnix): string
    {
        // 添加环境变量检查
        if (!isset($_ENV['SERVER_KEY']) || empty($_ENV['SERVER_KEY'])) {
            throw new \RuntimeException('SERVER_KEY not set in environment');
        }

        $ServerKey = $_ENV['SERVER_KEY'];

        if (!isset($_ENV['CLIENT_KEY']) || empty($_ENV['CLIENT_KEY'])) {
            throw new \RuntimeException('CLIENT_KEY not set in environment');
        }

        $ClientKey = $_ENV['CLIENT_KEY'];

        if (!isset($_ENV['KEY_T0']) || !ctype_digit($_ENV['KEY_T0'])) {
            throw new \RuntimeException('T0 not set or invalid');
        }

        $keyT0 = (int) $_ENV['KEY_T0'];

        // ---------- Payload 构造（64 bit） ----------
        if ($version < 0 || $version > 15) {
            throw new \InvalidArgumentException('Version range 0-15.');
        }
        if ($swId < 0 || $swId > 127) {
            throw new \InvalidArgumentException('SoftwareID range 0-127.');
        }

        // 21-bit 联系方式哈希
        $sha = hash('sha256', $contact, true);
        $contactBits = ((ord($sha[0]) << 13) | (ord($sha[1]) << 5) | (ord($sha[2]) >> 3)) & 0x1FFFFF;

        // 到期时间精确到小时
        $expireHour = intdiv(max($expireUnix - $keyT0, 0), 3600) & 0xFFFFFFFF;

        // 16-bit 随机数增强唯一性
        $rand16 = random_int(0, 0xFFFF);

        $payloadInt = ($version & 0xF)
            | (($swId & 0x7F) << 4)
            | (($contactBits & 0x1FFFFF) << 11)
            | ($expireHour << 32);

        // 小端 10 字节 Payload（64-bit 主字段 + 16-bit 随机）
        $payload = pack('V', $payloadInt & 0xFFFFFFFF) .
                   pack('V', ($payloadInt >> 32) & 0xFFFFFFFF) .
                   pack('v', $rand16);

        // ---------- MAC 计算 ----------
        $mac1 = substr(hash_hmac('sha256', $payload, base64_decode($ClientKey), true), 0, 4); // 32 bit
        $mac2 = substr(hash_hmac('sha256', $payload, base64_decode($ServerKey), true), 0, 8); // 64 bit

        $raw = $payload . $mac1 . $mac2; // 22 byte

        // ---------- Base32 编码 ----------
        $encoded = Base32::encode($raw); // 32 字符
        $encoded = str_pad($encoded, 36, '0', STR_PAD_LEFT); // 理论上已足够，但确保长度

        // 分段 6-6-6-6-6-6 (共 36 字符)
        return substr($encoded, 0, 6) . '-' .
               substr($encoded, 6, 6) . '-' .
               substr($encoded, 12, 6) . '-' .
               substr($encoded, 18, 6) . '-' .
               substr($encoded, 24, 6) . '-' .
               substr($encoded, 30, 6);
    }

    /**
     * 生成序列号，允许传入可读字符串形式的到期时间。
     *  示例："2025-12-31 23:59:59"、"+30 days"、"2026/01/01" 等。
     * @param int    $version   版本号 0-15
     * @param int    $swId      软件 ID 0-127
     * @param string $contact   用户联系方式
     * @param string $expireStr 到期时间字符串，可被 DateTime 解析
     * @param string $tz        时区标识，默认 'UTC'
     */
    public static function generateWithExpiryString(
        int $version,
        int $swId,
        string $contact,
        string $expireStr,
        string $tz = 'UTC'
    ): string {
        try {
            $dt = new \DateTimeImmutable($expireStr, new \DateTimeZone($tz));
        } catch (\Exception $e) {
            throw new \InvalidArgumentException('无法解析到期时间字符串: ' . $e->getMessage());
        }
        return self::generate($version, $swId, $contact, $dt->getTimestamp());
    }
} 