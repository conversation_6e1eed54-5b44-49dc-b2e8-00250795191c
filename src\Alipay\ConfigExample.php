﻿<?php

/**
 * 支付宝配置类使用示例
 * 
 * 展示如何使用 AlipayConfig 类进行配置管理
 * 
 * <AUTHOR> <<EMAIL>>
 * @version 1.0.0
 * @since 2024
 */

// 引入项目启动文件
require_once __DIR__ . '/../../boot.php';

use App\Lib\Alipay\Config;

echo "<h2>支付宝配置类使用示例</h2>";

// ========================================
// 示例1：使用默认配置创建实例
// ========================================
echo "<h3>1. 使用默认配置</h3>";

try {
    $config1 = Config::createDefault();
    echo "<p>✓ 默认配置创建成功</p>";
    echo "<p>应用ID: " . $config1->getAppId() . "</p>";
    echo "<p>网关地址: " . $config1->getGatewayUrl() . "</p>";
    echo "<p>字符编码: " . $config1->getCharset() . "</p>";
    echo "<p>签名方式: " . $config1->getSignType() . "</p>";
} catch (Exception $e) {
    echo "<p>✗ 默认配置创建失败: " . $e->getMessage() . "</p>";
}

// ========================================
// 示例2：从环境变量创建配置
// ========================================
echo "<h3>2. 从环境变量创建配置</h3>";

try {
    $config2 = Config::createFromEnv();
    echo "<p>✓ 环境变量配置创建成功</p>";
    
    // 检查配置是否完整
    if ($config2->isValid()) {
        echo "<p>✓ 配置验证通过</p>";
    } else {
        echo "<p>⚠ 配置不完整，缺失项: " . implode(', ', $config2->getMissingKeys()) . "</p>";
    }
} catch (Exception $e) {
    echo "<p>✗ 环境变量配置创建失败: " . $e->getMessage() . "</p>";
}

// ========================================
// 示例3：从自定义数组创建配置
// ========================================
echo "<h3>3. 从自定义数组创建配置</h3>";

try {
    $customConfig = [
        'app_id' => '2021002102688488',
        'notify_url' => 'https://pay.jiamisoft.com/alipay/notify',
        'return_url' => 'https://pay.jiamisoft.com/alipay/return',
        'charset' => 'GBK' // 自定义编码
    ];
    
    $config3 = Config::createFromArray($customConfig);
    echo "<p>✓ 自定义配置创建成功</p>";
    echo "<p>应用ID: " . $config3->getAppId() . "</p>";
    echo "<p>异步通知地址: " . $config3->getNotifyUrl() . "</p>";
    echo "<p>同步返回地址: " . $config3->getReturnUrl() . "</p>";
    echo "<p>字符编码: " . $config3->getCharset() . "</p>";
} catch (Exception $e) {
    echo "<p>✗ 自定义配置创建失败: " . $e->getMessage() . "</p>";
}

// ========================================
// 示例4：动态修改配置
// ========================================
echo "<h3>4. 动态修改配置</h3>";

try {
    $config4 = new Config();
    
    echo "<p>修改前的应用ID: " . $config4->getAppId() . "</p>";
    
    // 动态设置配置
    $config4->set('app_id', 'new_app_id_456')
            ->set('notify_url', 'https://new-domain.com/notify')
            ->set('return_url', 'https://new-domain.com/return');
    
    echo "<p>修改后的应用ID: " . $config4->getAppId() . "</p>";
    echo "<p>修改后的异步通知地址: " . $config4->getNotifyUrl() . "</p>";
    echo "<p>修改后的同步返回地址: " . $config4->getReturnUrl() . "</p>";
} catch (Exception $e) {
    echo "<p>✗ 动态修改配置失败: " . $e->getMessage() . "</p>";
}

// ========================================
// 示例5：配置验证和调试
// ========================================
echo "<h3>5. 配置验证和调试</h3>";

try {
    $config5 = new Config();
    
    // 验证配置完整性
    if ($config5->isValid()) {
        echo "<p>✓ 配置验证通过，所有必需项都已设置</p>";
    } else {
        $missingKeys = $config5->getMissingKeys();
        echo "<p>⚠ 配置验证失败，缺失以下必需项:</p>";
        echo "<ul>";
        foreach ($missingKeys as $key) {
            echo "<li>{$key}</li>";
        }
        echo "</ul>";
    }
    
    // 获取所有配置
    echo "<h4>所有配置项:</h4>";
    echo "<pre>";
    print_r($config5->getAll());
    echo "</pre>";
    
    // 转换为JSON格式
    echo "<h4>JSON格式配置:</h4>";
    echo "<pre>" . htmlspecialchars($config5->__toString()) . "</pre>";
    
} catch (Exception $e) {
    echo "<p>✗ 配置验证失败: " . $e->getMessage() . "</p>";
}

// ========================================
// 示例6：兼容性测试（转换为数组）
// ========================================
echo "<h3>6. 兼容性测试</h3>";

try {
    $config6 = new Config();
    
    // 转换为数组格式（兼容旧版本代码）
    $configArray = $config6->toArray();
    
    echo "<p>✓ 成功转换为数组格式，包含 " . count($configArray) . " 个配置项</p>";
    
    // 模拟旧版本代码使用方式
    echo "<p>旧版本兼容测试:</p>";
    echo "<ul>";
    echo "<li>应用ID: " . ($configArray['app_id'] ?? '未设置') . "</li>";
    echo "<li>网关地址: " . ($configArray['gateway_url'] ?? '未设置') . "</li>";
    echo "<li>字符编码: " . ($configArray['charset'] ?? '未设置') . "</li>";
    echo "<li>签名方式: " . ($configArray['sign_type'] ?? '未设置') . "</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p>✗ 兼容性测试失败: " . $e->getMessage() . "</p>";
}

// ========================================
// 示例7：实际使用场景
// ========================================
echo "<h3>7. 实际使用场景</h3>";

try {
    // 在实际项目中的使用方式
    $alipayConfig = new Config();
    
    // 检查配置是否可用
    if (!$alipayConfig->isValid()) {
        throw new Exception('支付宝配置不完整，请检查: ' . implode(', ', $alipayConfig->getMissingKeys()));
    }
    
    // 模拟支付宝SDK初始化
    echo "<p>✓ 支付宝配置验证通过，可以初始化SDK</p>";
    echo "<p>使用的配置:</p>";
    echo "<ul>";
    echo "<li>应用ID: " . $alipayConfig->getAppId() . "</li>";
    echo "<li>网关地址: " . $alipayConfig->getGatewayUrl() . "</li>";
    echo "<li>异步通知地址: " . $alipayConfig->getNotifyUrl() . "</li>";
    echo "<li>同步返回地址: " . $alipayConfig->getReturnUrl() . "</li>";
    echo "</ul>";
    
    // 模拟传递给支付宝SDK
    $sdkConfig = $alipayConfig->toArray();
    echo "<p>✓ 配置已准备好传递给支付宝SDK</p>";
    
} catch (Exception $e) {
    echo "<p>✗ 实际使用场景测试失败: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><strong>使用说明:</strong></p>";
echo "<ol>";
echo "<li>推荐使用环境变量方式配置支付宝参数</li>";
echo "<li>在生产环境中，请确保所有必需的配置项都已正确设置</li>";
echo "<li>可以使用 isValid() 方法验证配置完整性</li>";
echo "<li>支持动态修改配置，适用于多租户场景</li>";
echo "<li>提供了完整的getter方法，方便获取特定配置项</li>";
echo "<li>兼容旧版本代码，可以通过 toArray() 方法转换为数组格式</li>";
echo "</ol>";

echo "<p><strong>安全提醒:</strong></p>";
echo "<ul>";
echo "<li>私钥信息非常敏感，请妥善保管</li>";
echo "<li>建议使用环境变量存储敏感配置</li>";
echo "<li>定期更换密钥以确保安全</li>";
echo "<li>在版本控制中排除包含真实密钥的配置文件</li>";
echo "</ul>";
?>