<?php
declare(strict_types=1);
namespace App\Lib;
/**
 * Crockford Base32 编解码，全部大写字符集：
 * 0123456789ABCDEFGHJKMNPQRSTVWXYZ
 * 不使用 I/L/O/U，避免视觉混淆。
 * 纯 PHP 实现，无需 GMP/BCMath。
 */
final class Base32
{
    private const ALPHABET = '0123456789ABCDEFGHJKMNPQRSTVWXYZ';
    private const ALPHABET_MAP = [
        '0' => 0,'1' => 1,'2' => 2,'3' => 3,'4' => 4,'5' => 5,'6' => 6,'7' => 7,'8' => 8,'9' => 9,
        'A' => 10,'B' => 11,'C' => 12,'D' => 13,'E' => 14,'F' => 15,'G' => 16,'H' => 17,'J' => 18,'K' => 19,
        'M' => 20,'N' => 21,'P' => 22,'Q' => 23,'R' => 24,'S' => 25,'T' => 26,'V' => 27,'W' => 28,'X' => 29,
        'Y' => 30,'Z' => 31
    ];

    public static function encode(string $data): string
    {
        if ($data === '') {
            return '';
        }
        $bits = '';
        foreach (str_split($data) as $byte) {
            $bits .= str_pad(decbin(ord($byte)), 8, '0', STR_PAD_LEFT);
        }
        $encoded = '';
        foreach (str_split($bits, 5) as $chunk) {
            if (strlen($chunk) < 5) {
                $chunk = str_pad($chunk, 5, '0', STR_PAD_RIGHT);
            }
            $encoded .= self::ALPHABET[bindec($chunk)];
        }
        return $encoded;
    }

    public static function decode(string $str): string
    {
        $str = strtoupper($str);
        $bits = '';
        foreach (str_split($str) as $char) {
            if (!isset(self::ALPHABET_MAP[$char])) {
                throw new \InvalidArgumentException('Invalid Base32 character: ' . $char);
            }
            $bits .= str_pad(decbin(self::ALPHABET_MAP[$char]), 5, '0', STR_PAD_LEFT);
        }
        $bytes = '';
        foreach (str_split($bits, 8) as $chunk) {
            if (strlen($chunk) < 8) {
                continue; // 剩余不足 8 位的填充位丢弃
            }
            $bytes .= chr(bindec($chunk));
        }
        return $bytes;
    }
} 